# Frontend Developer Guide - Universal Wallet Platform

This comprehensive guide provides frontend developers with everything needed to integrate with the Universal Wallet Platform API. The platform supports both regular users and admin functionality with a complete KYC workflow.

## 🚀 Quick Start

### Base URL
```
Development: http://localhost:8000/api/v1
Production: https://your-domain.com/api/v1
```

### Authentication
All authenticated endpoints require a Bearer token in the Authorization header:
```javascript
headers: {
  'Authorization': `Bearer ${accessToken}`,
  'Content-Type': 'application/json'
}
```

## 🔐 Authentication Flow

### 1. User Registration (Public)
```javascript
const registerUser = async (userData) => {
  const response = await fetch('/api/v1/auth/register', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email: userData.email,
      password: userData.password,
      first_name: userData.firstName,
      last_name: userData.lastName
    })
  });
  return response.json();
};

// Response format (NEW):
// {
//   "message": "Registration successful. Your account is pending admin approval.",
//   "user_id": "uuid-string",
//   "email": "<EMAIL>",
//   "is_active": false
// }

// Important Notes:
// - Rate limited: 3 registrations per 30 minutes per IP address
// - Users are created as INACTIVE and require admin approval
// - Automatic profile and wallet creation (fiat USD + crypto BTC)
// - Users cannot login until activated by admin
```

### 2. User Login
```javascript
const loginUser = async (email, password) => {
  const formData = new FormData();
  formData.append('username', email);
  formData.append('password', password);

  const response = await fetch('/api/v1/auth/login', {
    method: 'POST',
    body: formData
  });

  const data = await response.json();
  // Store tokens securely
  localStorage.setItem('accessToken', data.access_token);
  localStorage.setItem('refreshToken', data.refresh_token);
  return data;
};
```

### 3. Token Refresh
```javascript
const refreshToken = async () => {
  const refreshToken = localStorage.getItem('refreshToken');
  const response = await fetch('/api/v1/auth/refresh', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ refresh_token: refreshToken })
  });

  const data = await response.json();
  localStorage.setItem('accessToken', data.access_token);
  return data.access_token;
};
```

## 👤 User Management

### Get Current User Profile
```javascript
const getCurrentUser = async () => {
  const response = await fetch('/api/v1/users/me', {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};

// Response includes:
// - id (UUID)
// - email
// - first_name, last_name
// - role (USER, ADMIN, ACCOUNT_MANAGER)
// - is_active
// - fiat_withdraw_limit
// - crypto_withdraw_limit
// - created_at, updated_at
```

## 📋 KYC Profile Management

### Get User Profile
```javascript
const getUserProfile = async () => {
  const response = await fetch('/api/v1/profiles/me', {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};
```

### Update User Profile
```javascript
const updateProfile = async (profileData) => {
  const response = await fetch('/api/v1/profiles/me', {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      phone_number: profileData.phoneNumber,
      date_of_birth: profileData.dateOfBirth, // YYYY-MM-DD format
      address_line_1: profileData.addressLine1,
      address_line_2: profileData.addressLine2,
      city: profileData.city,
      state_province: profileData.stateProvince,
      postal_code: profileData.postalCode,
      country: profileData.country,
      id_document_type: profileData.idDocumentType, // passport, drivers_license, national_id
      id_document_number: profileData.idDocumentNumber
    })
  });
  return response.json();
};
```

### Upload Avatar Image
```javascript
const uploadAvatar = async (imageFile) => {
  const formData = new FormData();
  formData.append('file', imageFile);

  const response = await fetch('/api/v1/profiles/me/avatar', {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${accessToken}` },
    body: formData
  });
  return response.json();
};

// Constraints:
// - Max file size: 5MB
// - Allowed formats: JPEG, PNG
// - Image is stored as binary data
```

### Submit KYC for Review
```javascript
const submitKYC = async () => {
  const response = await fetch('/api/v1/profiles/me/submit-kyc', {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};

// KYC Status Flow:
// PENDING → UNDER_REVIEW → APPROVED/REJECTED
```

## 💰 Wallet Management

### Get Fiat Wallet
```javascript
const getFiatWallet = async () => {
  const response = await fetch('/api/v1/wallets/fiat/me', {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};

// Response includes:
// - id (UUID)
// - balance (decimal)
// - currency (USD)
// - created_at, updated_at
```

### Get Crypto Wallet
```javascript
const getCryptoWallet = async () => {
  const response = await fetch('/api/v1/wallets/crypto/me', {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};

// Response includes:
// - id (UUID)
// - balance (decimal)
// - currency (BTC)
// - wallet_address (string)
// - created_at, updated_at
```

## 💸 Transaction Management

### Get User Transactions (Paginated)
```javascript
const getTransactions = async (page = 1, pageSize = 20) => {
  const response = await fetch(`/api/v1/transactions/me?page=${page}&page_size=${pageSize}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};

// Response format (Django-style pagination):
// {
//   "count": 150,                    // Total number of transactions
//   "next": "http://api/transactions/me?page=2&page_size=20",
//   "previous": null,                // URL for previous page (null if first page)
//   "results": [...]                 // Array of transaction objects
// }
```

### Create Withdrawal
```javascript
const createWithdrawal = async (withdrawalData) => {
  const response = await fetch('/api/v1/transactions/withdraw', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      amount: withdrawalData.amount,
      wallet_type: withdrawalData.walletType, // "fiat" or "crypto"
      withdrawal_method: withdrawalData.method, // "wire_transfer", "crypto", etc.
      description: withdrawalData.description
    })
  });
  return response.json();
};

// Note: Withdrawals are subject to daily limits
// - Fiat limit: configurable per user (default $10,000)
// - Crypto limit: configurable per user (default 1 BTC)
```

### Convert Between Wallets
```javascript
const convertFunds = async (conversionData) => {
  const response = await fetch('/api/v1/transactions/convert', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      from_wallet_type: conversionData.fromWalletType, // "fiat" or "crypto"
      to_wallet_type: conversionData.toWalletType,     // "fiat" or "crypto"
      amount: conversionData.amount,
      description: conversionData.description
    })
  });
  return response.json();
};
```

## 📄 Document Management

### Upload KYC Document
```javascript
const uploadDocument = async (documentType, file) => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch(`/api/v1/documents/upload?document_type=${documentType}`, {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${accessToken}` },
    body: formData
  });
  return response.json();
};

// Document types:
// - "id_document" (passport, driver's license, national ID)
// - "proof_of_address" (utility bill, bank statement)
// - "selfie_with_id" (selfie holding ID document)

// Constraints:
// - Max file size: 10MB
// - Allowed formats: PDF, JPG, PNG
// - One document per type per user
```

### Get User Documents Summary
```javascript
const getMyDocuments = async () => {
  const response = await fetch('/api/v1/documents/me', {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};

// Response includes:
// - user_id
// - total_documents
// - pending_documents
// - approved_documents
// - rejected_documents
// - documents: array of document summaries
```

### Delete Document
```javascript
const deleteDocument = async (documentId) => {
  const response = await fetch(`/api/v1/documents/${documentId}`, {
    method: 'DELETE',
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};

// Note: Only non-approved documents can be deleted
```

## 📊 Pagination

All list endpoints now support Django-style pagination:

### Pagination Parameters
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 20, max: 100)

### Pagination Response Format
```javascript
{
  "count": 150,                    // Total number of items
  "next": "http://api/endpoint?page=3&page_size=20",  // Next page URL
  "previous": "http://api/endpoint?page=1&page_size=20", // Previous page URL
  "results": [...]                 // Current page items
}
```

### Pagination Helper Function
```javascript
const handlePagination = async (url, page = 1, pageSize = 20, filters = {}) => {
  const params = new URLSearchParams({
    page: page.toString(),
    page_size: pageSize.toString(),
    ...filters
  });

  const response = await fetch(`${url}?${params}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};

// Usage examples:
// Get users (admin)
const users = await handlePagination('/api/v1/users', 1, 20, { role: 'USER' });

// Get transactions
const transactions = await handlePagination('/api/v1/transactions/me', 2, 10);

// Get profiles (admin)
const profiles = await handlePagination('/api/v1/admin/profiles', 1, 50, { kyc_status: 'PENDING' });
```

## 🎫 Support Tickets

### Create Support Ticket
```javascript
const createSupportTicket = async (ticketData) => {
  const response = await fetch('/api/v1/support/tickets', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      subject: ticketData.subject,
      description: ticketData.description,
      priority: ticketData.priority // "low", "medium", "high", "urgent"
    })
  });
  return response.json();
};
```

### Get User Support Tickets
```javascript
const getSupportTickets = async () => {
  const response = await fetch('/api/v1/support/tickets/me', {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};
```

## 🔒 Password Reset

### Request Password Reset
```javascript
const requestPasswordReset = async (email) => {
  const response = await fetch('/api/v1/password-reset/request', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email })
  });
  return response.json();
};
```

### Reset Password with Token
```javascript
const resetPassword = async (token, newPassword) => {
  const response = await fetch('/api/v1/password-reset/reset', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      token,
      new_password: newPassword
    })
  });
  return response.json();
};
```

## 👑 Admin Functions

**Note**: All admin functions require the user to have `ADMIN` role. Admin endpoints are tagged with 'admin' in the API documentation.

### User Management

#### Get All Users (Paginated)
```javascript
const getAllUsers = async (page = 1, pageSize = 20, role = 'USER') => {
  const response = await fetch(`/api/v1/users?page=${page}&page_size=${pageSize}&role=${role}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};

// By default, only returns users with role=USER (excludes ADMIN and ACCOUNT_MANAGER)
// Response uses Django-style pagination format
```

#### Update User Withdraw Limits
```javascript
const updateUserWithdrawLimits = async (userId, limits) => {
  const response = await fetch(`/api/v1/users/${userId}/withdraw-limits`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      fiat_withdraw_limit: limits.fiatLimit,    // e.g., 10000.00
      crypto_withdraw_limit: limits.cryptoLimit // e.g., 1.0
    })
  });
  return response.json();
};
```

#### Get User Available Withdraw Limits
```javascript
const getUserWithdrawLimits = async (userId) => {
  const response = await fetch(`/api/v1/users/me/withdraw-limits`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};

// Response includes:
// - fiat_limit: total fiat withdraw limit
// - fiat_used: amount already withdrawn
// - fiat_available: remaining fiat limit
// - crypto_limit: total crypto withdraw limit
// - crypto_used: amount already withdrawn
// - crypto_available: remaining crypto limit
```

#### Reset User Withdraw Usage
```javascript
const resetUserWithdrawUsage = async (userId, resetOptions) => {
  const params = new URLSearchParams();
  if (resetOptions.resetFiat) params.append('reset_fiat', 'true');
  if (resetOptions.resetCrypto) params.append('reset_crypto', 'true');

  const response = await fetch(`/api/v1/users/${userId}/reset-withdraw-usage?${params}`, {
    method: 'PUT',
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};

// Example usage:
// Reset both fiat and crypto usage
await resetUserWithdrawUsage(userId, { resetFiat: true, resetCrypto: true });

// Reset only fiat usage
await resetUserWithdrawUsage(userId, { resetFiat: true, resetCrypto: false });
```

#### Reset User Password
```javascript
const resetUserPassword = async (userId, newPassword = null) => {
  const response = await fetch(`/api/v1/users/${userId}/reset-password`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      new_password: newPassword // If null/undefined, defaults to "default123"
    })
  });
  return response.json();
};

// Example usage:
// Reset to custom password
await resetUserPassword(userId, "newSecurePassword123");

// Reset to default password "default123"
await resetUserPassword(userId);
// or
await resetUserPassword(userId, null);
```

### KYC Management

#### Get User Profile (Admin)
```javascript
const getUserProfileAdmin = async (userId) => {
  const response = await fetch(`/api/v1/profiles/${userId}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};
```

#### Review KYC Submission
```javascript
const reviewKYC = async (userId, reviewData) => {
  const response = await fetch(`/api/v1/profiles/${userId}/kyc-review`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      kyc_status: reviewData.status, // "approved" or "rejected"
      kyc_rejection_reason: reviewData.rejectionReason // required if rejected
    })
  });
  return response.json();
};

// Example usage:
// Approve KYC
await reviewKYC(userId, { status: 'approved' });

// Reject KYC
await reviewKYC(userId, {
  status: 'rejected',
  rejectionReason: 'Invalid document provided'
});
```

### Admin Profile Management

#### Get All Profiles (Paginated)
```javascript
const getAllProfiles = async (page = 1, pageSize = 20, kycStatus = null) => {
  const params = new URLSearchParams({
    page: page.toString(),
    page_size: pageSize.toString()
  });

  if (kycStatus) {
    params.append('kyc_status', kycStatus);
  }

  const response = await fetch(`/api/v1/admin/profiles?${params}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};

// Filter by KYC status: PENDING, UNDER_REVIEW, APPROVED, REJECTED
```

#### Update User Profile (Admin)
```javascript
const updateUserProfileAdmin = async (userId, profileData) => {
  const response = await fetch(`/api/v1/admin/profiles/${userId}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(profileData)
  });
  return response.json();
};
```

#### Update KYC Status
```javascript
const updateKYCStatus = async (userId, status, rejectionReason = null) => {
  const response = await fetch(`/api/v1/admin/profiles/${userId}/kyc-status`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      kyc_status: status,
      kyc_rejection_reason: rejectionReason
    })
  });
  return response.json();
};

// Valid transitions:
// PENDING → UNDER_REVIEW
// UNDER_REVIEW → APPROVED/REJECTED
// Any status → PENDING (for re-review)
```

### Admin Document Management

#### Get User Documents
```javascript
const getUserDocuments = async (userId) => {
  const response = await fetch(`/api/v1/documents/admin/users/${userId}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};
```

#### Get Document with File Data
```javascript
const getDocumentWithData = async (userId, documentId) => {
  const response = await fetch(`/api/v1/documents/admin/users/${userId}/${documentId}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};

// Returns document with file_data (base64 encoded binary data)
// Use for document review and verification
```

#### Verify/Reject Document
```javascript
const verifyDocument = async (userId, documentId, status, rejectionReason = null) => {
  const response = await fetch(`/api/v1/documents/admin/users/${userId}/${documentId}/verify`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      status: status, // "approved", "rejected", "under_review"
      rejection_reason: rejectionReason
    })
  });
  return response.json();
};

// Example usage:
// Approve document
await verifyDocument(userId, documentId, 'approved');

// Reject document
await verifyDocument(userId, documentId, 'rejected', 'Document is not clear enough');
```

#### Get Pending Documents
```javascript
const getPendingDocuments = async (skip = 0, limit = 100) => {
  const response = await fetch(`/api/v1/documents/admin/pending?skip=${skip}&limit=${limit}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};

// Returns all documents with status=PENDING for admin review
```

### Wallet Configuration Management

#### Get All Wallet Configurations
```javascript
const getWalletConfigs = async () => {
  const response = await fetch('/api/v1/admin/wallet-configs', {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};
```

#### Create Wallet Configuration
```javascript
const createWalletConfig = async (configData) => {
  const response = await fetch('/api/v1/admin/wallet-configs', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      config_type: "default_crypto_address",
      currency: configData.currency,        // e.g., "BTC", "ETH"
      default_address: configData.address,  // crypto wallet address
      description: configData.description,
      is_active: true,
      created_by: currentUserId,
      updated_by: currentUserId
    })
  });
  return response.json();
};
```

#### Update Wallet Configuration
```javascript
const updateWalletConfig = async (configId, updateData) => {
  const response = await fetch(`/api/v1/admin/wallet-configs/${configId}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      default_address: updateData.address,
      description: updateData.description,
      updated_by: currentUserId
    })
  });
  return response.json();
};
```

#### Get Wallet Configuration by Currency
```javascript
const getWalletConfigByCurrency = async (currency) => {
  const response = await fetch(`/api/v1/admin/wallet-configs/currency/${currency}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};
```

#### Deactivate Wallet Configuration
```javascript
const deactivateWalletConfig = async (configId) => {
  const response = await fetch(`/api/v1/admin/wallet-configs/${configId}`, {
    method: 'DELETE',
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};
```

### User Crypto Wallet Management

#### Update User's Crypto Wallet Address
```javascript
const updateUserCryptoAddress = async (userId, newAddress) => {
  const response = await fetch(`/api/v1/admin/users/${userId}/crypto-wallet-address`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      wallet_address: newAddress
    })
  });
  return response.json();
};
```

### Transaction Management (Admin)

#### Create Deposit for User
```javascript
const createDeposit = async (depositData) => {
  const response = await fetch('/api/v1/transactions/deposit', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      user_id: depositData.userId,
      amount: depositData.amount,
      wallet_type: depositData.walletType, // "fiat" or "crypto"
      description: depositData.description
    })
  });
  return response.json();
};
```

#### Get All Transactions (Admin)
```javascript
const getAllTransactions = async (skip = 0, limit = 100) => {
  const response = await fetch(`/api/v1/transactions?skip=${skip}&limit=${limit}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  return response.json();
};
```

## 🔄 Data Models & Types

### User Object
```typescript
interface User {
  id: string;                    // UUID
  email: string;
  first_name: string;
  last_name: string;
  role: 'USER' | 'ADMIN' | 'ACCOUNT_MANAGER';
  is_active: boolean;
  fiat_withdraw_limit: number;   // Default: 10000.00
  crypto_withdraw_limit: number; // Default: 1.0
  created_at: string;           // ISO datetime
  updated_at: string;           // ISO datetime
}
```

### User Profile Object
```typescript
interface UserProfile {
  id: string;                    // UUID
  user_id: string;              // UUID
  phone_number?: string;
  date_of_birth?: string;       // YYYY-MM-DD
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  state_province?: string;
  postal_code?: string;
  country?: string;
  id_document_type?: 'passport' | 'drivers_license' | 'national_id';
  id_document_number?: string;
  kyc_status: 'pending' | 'under_review' | 'approved' | 'rejected';
  kyc_submitted_at?: string;    // ISO datetime
  kyc_reviewed_at?: string;     // ISO datetime
  kyc_reviewed_by?: string;     // UUID
  kyc_rejection_reason?: string;
  avatar_image?: string;        // Binary data (base64 when retrieved)
  created_at: string;
  updated_at: string;
}
```

### Wallet Objects
```typescript
interface FiatWallet {
  id: string;                   // UUID
  user_id: string;             // UUID
  balance: number;             // Decimal
  currency: 'USD';             // Currently only USD supported
  created_at: string;
  updated_at: string;
}

interface CryptoWallet {
  id: string;                  // UUID
  user_id: string;            // UUID
  balance: number;            // Decimal
  currency: 'BTC';            // Currently only BTC supported
  wallet_address: string;     // Crypto wallet address
  created_at: string;
  updated_at: string;
}
```

### Transaction Object
```typescript
interface Transaction {
  id: string;                           // UUID
  user_id: string;                     // UUID
  transaction_type: 'deposit' | 'withdrawal' | 'conversion';
  amount: number;                      // Decimal
  currency: string;                    // USD, BTC
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  description?: string;
  from_wallet_type?: 'fiat' | 'crypto';
  to_wallet_type?: 'fiat' | 'crypto';
  withdrawal_method?: string;          // wire_transfer, crypto, etc.
  created_at: string;
  updated_at: string;
}
```

### Support Ticket Object
```typescript
interface SupportTicket {
  id: string;                          // UUID
  user_id: string;                    // UUID
  subject: string;
  description: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assigned_to?: string;               // UUID of account manager
  created_at: string;
  updated_at: string;
}
```

## ⚠️ Error Handling

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `422` - Unprocessable Entity (validation errors)
- `500` - Internal Server Error

### Error Response Format
```typescript
interface ErrorResponse {
  detail: string;               // Error message
  type?: string;               // Error type
  errors?: ValidationError[];  // Field validation errors
}

interface ValidationError {
  loc: string[];              // Field location
  msg: string;                // Error message
  type: string;               // Error type
}
```

### Example Error Handling
```javascript
const handleApiCall = async (apiFunction) => {
  try {
    const response = await apiFunction();
    return { success: true, data: response };
  } catch (error) {
    if (error.status === 401) {
      // Token expired, try to refresh
      try {
        await refreshToken();
        const retryResponse = await apiFunction();
        return { success: true, data: retryResponse };
      } catch (refreshError) {
        // Redirect to login
        window.location.href = '/login';
        return { success: false, error: 'Authentication failed' };
      }
    } else if (error.status === 403) {
      return { success: false, error: 'Insufficient permissions' };
    } else if (error.status === 400 || error.status === 422) {
      const errorData = await error.json();
      return { success: false, error: errorData.detail, validationErrors: errorData.errors };
    } else {
      return { success: false, error: 'An unexpected error occurred' };
    }
  }
};
```

## 🛡️ Security Best Practices

### Token Management
1. **Store tokens securely** (consider httpOnly cookies for production)
2. **Implement automatic token refresh** before expiration
3. **Clear tokens on logout** and redirect to login page
4. **Validate token expiration** before making API calls

### Input Validation
1. **Validate all user inputs** on the frontend before sending to API
2. **Sanitize file uploads** (check file type, size, content)
3. **Use proper form validation** for email, phone numbers, etc.
4. **Implement client-side rate limiting** for sensitive operations

### File Upload Security
```javascript
const validateImageFile = (file) => {
  const allowedTypes = ['image/jpeg', 'image/png'];
  const maxSize = 5 * 1024 * 1024; // 5MB

  if (!allowedTypes.includes(file.type)) {
    throw new Error('Only JPEG and PNG files are allowed');
  }

  if (file.size > maxSize) {
    throw new Error('File size must be less than 5MB');
  }

  return true;
};
```

## 🎨 UI/UX Recommendations

### KYC Status Display
```javascript
const getKYCStatusDisplay = (status) => {
  const statusConfig = {
    pending: { color: 'orange', text: 'Pending Review', icon: '⏳' },
    under_review: { color: 'blue', text: 'Under Review', icon: '🔍' },
    approved: { color: 'green', text: 'Approved', icon: '✅' },
    rejected: { color: 'red', text: 'Rejected', icon: '❌' }
  };
  return statusConfig[status] || statusConfig.pending;
};
```

### Withdraw Limit Warnings
```javascript
const checkWithdrawLimit = (amount, walletType, user) => {
  const limit = walletType === 'fiat'
    ? user.fiat_withdraw_limit
    : user.crypto_withdraw_limit;

  if (amount > limit) {
    return {
      canWithdraw: false,
      message: `Amount exceeds daily limit of ${limit} ${walletType === 'fiat' ? 'USD' : 'BTC'}`
    };
  }

  const warningThreshold = limit * 0.8;
  if (amount > warningThreshold) {
    return {
      canWithdraw: true,
      warning: `This withdrawal is close to your daily limit of ${limit}`
    };
  }

  return { canWithdraw: true };
};
```

## 📱 Mobile Considerations

### Responsive Design
- Use responsive units and breakpoints
- Optimize touch targets for mobile devices
- Consider mobile-specific UX patterns for financial apps

### File Upload on Mobile
```javascript
const handleMobileImageUpload = () => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/jpeg,image/png';
  input.capture = 'environment'; // Use camera if available

  input.onchange = (event) => {
    const file = event.target.files[0];
    if (file && validateImageFile(file)) {
      uploadAvatar(file);
    }
  };

  input.click();
};
```

## 🔗 Integration Examples

### React Hook Example
```javascript
import { useState, useEffect } from 'react';

const useWalletData = () => {
  const [fiatWallet, setFiatWallet] = useState(null);
  const [cryptoWallet, setCryptoWallet] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchWallets = async () => {
      try {
        setLoading(true);
        const [fiat, crypto] = await Promise.all([
          getFiatWallet(),
          getCryptoWallet()
        ]);
        setFiatWallet(fiat);
        setCryptoWallet(crypto);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchWallets();
  }, []);

  return { fiatWallet, cryptoWallet, loading, error };
};
```

### Vue.js Composition API Example
```javascript
import { ref, onMounted } from 'vue';

export function useUserProfile() {
  const profile = ref(null);
  const loading = ref(false);
  const error = ref(null);

  const fetchProfile = async () => {
    try {
      loading.value = true;
      profile.value = await getUserProfile();
    } catch (err) {
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  };

  const updateProfile = async (profileData) => {
    try {
      loading.value = true;
      const updated = await updateProfile(profileData);
      profile.value = updated;
      return updated;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  onMounted(fetchProfile);

  return {
    profile,
    loading,
    error,
    fetchProfile,
    updateProfile
  };
}
```

## 📞 Support & Resources

### API Documentation
- **Swagger UI**: http://localhost:8000/api/v1/docs
- **ReDoc**: http://localhost:8000/api/v1/redoc
- **OpenAPI Schema**: http://localhost:8000/api/v1/openapi.json

### Testing
- Use the interactive Swagger UI to test API endpoints
- All endpoints include example requests and responses
- Admin endpoints are clearly marked with the 'admin' tag

### Common Issues
1. **CORS Issues**: Ensure your frontend domain is in the CORS allowed origins
2. **Token Expiration**: Implement proper token refresh logic
3. **File Upload Limits**: Respect the 5MB limit for avatar uploads
4. **Withdraw Limits**: Check user limits before allowing withdrawal attempts

This guide provides comprehensive coverage of all API endpoints and best practices for integrating with the Universal Wallet Platform. For additional support, refer to the API documentation or create an issue in the project repository.
