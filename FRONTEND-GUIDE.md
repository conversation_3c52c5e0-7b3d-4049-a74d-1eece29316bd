# Universal Wallet Platform - Frontend Developer Guide

⚠️ **NOTICE: This guide has been moved and updated!**

This file has been replaced with a comprehensive, up-to-date frontend developer guide that includes all the latest features including KYC workflows, admin functionality, UUID system, and more.

## 📍 New Location

**Please refer to the new comprehensive guide:**
**[docs/frontend-guide.md](docs/frontend-guide.md)**

## 🆕 What's New in the Updated Guide

The new guide includes:

### ✅ **Complete Coverage**
- **KYC Profile System** - Complete Know Your Customer workflow
- **Admin Functions** - User management, wallet configuration, KYC review
- **UUID System** - All entities now use UUIDs for enhanced security
- **Avatar Upload** - Profile image support with validation
- **Withdraw Limits** - Admin-controlled risk management
- **Enhanced Security** - Updated authentication patterns

### ✅ **Better Organization**
- **TypeScript Interfaces** - Complete data model definitions
- **Error Handling** - Comprehensive error handling patterns
- **Security Best Practices** - Token management and validation
- **UI/UX Recommendations** - Status displays and user experience tips
- **Framework Examples** - React and Vue.js integration examples

### ✅ **Production Ready**
- **Mobile Considerations** - Responsive design and mobile-specific patterns
- **Testing Guidelines** - How to test API integrations
- **Performance Tips** - Optimization recommendations
- **Common Issues** - Troubleshooting guide

## 🚀 Quick Migration

If you were using this old guide, here are the key changes:

1. **Base URL remains the same**: `http://localhost:8000/api/v1`
2. **All IDs are now UUIDs** instead of integers
3. **New KYC endpoints** for profile management
4. **New admin endpoints** for system management
5. **Enhanced authentication** with refresh token support

## 📚 API Documentation

- **Interactive Swagger UI**: http://localhost:8000/api/v1/docs
- **ReDoc Documentation**: http://localhost:8000/api/v1/redoc
- **Complete Frontend Guide**: [docs/frontend-guide.md](docs/frontend-guide.md)

---

**🔗 For the complete, up-to-date frontend integration guide with all the latest features, please visit:**
**[docs/frontend-guide.md](docs/frontend-guide.md)**
