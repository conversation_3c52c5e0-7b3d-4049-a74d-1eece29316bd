import pytest
import time
from unittest.mock import Mock

from app.utils.rate_limiter import InM<PERSON>oryRateLimiter, get_client_ip, check_rate_limit
from fastapi import HTTP<PERSON>x<PERSON>, Request


def test_rate_limiter_allows_requests_under_limit():
    """Test that rate limiter allows requests under the limit."""
    limiter = InMemoryRateLimiter()
    
    # Should allow 3 requests within the limit
    assert limiter.is_allowed("***********", max_requests=3, window_seconds=60) is True
    assert limiter.is_allowed("***********", max_requests=3, window_seconds=60) is True
    assert limiter.is_allowed("***********", max_requests=3, window_seconds=60) is True


def test_rate_limiter_blocks_requests_over_limit():
    """Test that rate limiter blocks requests over the limit."""
    limiter = InMemoryRateLimiter()
    
    # Use up the limit
    for _ in range(3):
        assert limiter.is_allowed("***********", max_requests=3, window_seconds=60) is True
    
    # Next request should be blocked
    assert limiter.is_allowed("***********", max_requests=3, window_seconds=60) is False


def test_rate_limiter_different_ips_independent():
    """Test that different IPs have independent rate limits."""
    limiter = InMemoryRateLimiter()
    
    # Use up limit for first IP
    for _ in range(3):
        assert limiter.is_allowed("***********", max_requests=3, window_seconds=60) is True
    
    # Should be blocked
    assert limiter.is_allowed("***********", max_requests=3, window_seconds=60) is False
    
    # Different IP should still be allowed
    assert limiter.is_allowed("***********", max_requests=3, window_seconds=60) is True


def test_rate_limiter_window_expiry():
    """Test that rate limiter resets after window expires."""
    limiter = InMemoryRateLimiter()
    
    # Use up the limit
    for _ in range(3):
        assert limiter.is_allowed("***********", max_requests=3, window_seconds=1) is True
    
    # Should be blocked
    assert limiter.is_allowed("***********", max_requests=3, window_seconds=1) is False
    
    # Wait for window to expire
    time.sleep(1.1)
    
    # Should be allowed again
    assert limiter.is_allowed("***********", max_requests=3, window_seconds=1) is True


def test_rate_limiter_cleanup():
    """Test that cleanup removes old entries."""
    limiter = InMemoryRateLimiter()
    
    # Add some requests
    limiter.is_allowed("***********", max_requests=3, window_seconds=60)
    limiter.is_allowed("***********", max_requests=3, window_seconds=60)
    
    assert len(limiter._requests) == 2
    
    # Cleanup with very short max_age should remove all entries
    limiter.cleanup_old_entries(max_age_seconds=0)
    
    assert len(limiter._requests) == 0


def test_get_client_ip_direct():
    """Test getting client IP from direct connection."""
    request = Mock(spec=Request)
    request.headers = {}
    request.client = Mock()
    request.client.host = "***********00"
    
    ip = get_client_ip(request)
    assert ip == "***********00"


def test_get_client_ip_forwarded_for():
    """Test getting client IP from X-Forwarded-For header."""
    request = Mock(spec=Request)
    request.headers = {"X-Forwarded-For": "***********, ***********00"}
    request.client = Mock()
    request.client.host = "***********00"
    
    ip = get_client_ip(request)
    assert ip == "***********"  # Should get the first IP


def test_get_client_ip_real_ip():
    """Test getting client IP from X-Real-IP header."""
    request = Mock(spec=Request)
    request.headers = {"X-Real-IP": "***********"}
    request.client = Mock()
    request.client.host = "***********00"
    
    ip = get_client_ip(request)
    assert ip == "***********"


def test_get_client_ip_no_client():
    """Test getting client IP when no client info available."""
    request = Mock(spec=Request)
    request.headers = {}
    request.client = None
    
    ip = get_client_ip(request)
    assert ip == "unknown"


def test_check_rate_limit_allowed():
    """Test check_rate_limit function when request is allowed."""
    request = Mock(spec=Request)
    request.headers = {}
    request.client = Mock()
    request.client.host = "***********00"
    
    # Should not raise exception
    check_rate_limit(request, max_requests=5, window_seconds=60)


def test_check_rate_limit_exceeded():
    """Test check_rate_limit function when rate limit is exceeded."""
    request = Mock(spec=Request)
    request.headers = {}
    request.client = Mock()
    request.client.host = "***********01"
    
    # Use up the limit
    for _ in range(3):
        check_rate_limit(request, max_requests=3, window_seconds=60)
    
    # Next request should raise HTTPException
    with pytest.raises(HTTPException) as exc_info:
        check_rate_limit(request, max_requests=3, window_seconds=60)
    
    assert exc_info.value.status_code == 429
    assert "Rate limit exceeded" in exc_info.value.detail


def test_check_rate_limit_custom_message():
    """Test check_rate_limit with custom error message."""
    request = Mock(spec=Request)
    request.headers = {}
    request.client = Mock()
    request.client.host = "***********02"
    
    # Use up the limit
    for _ in range(2):
        check_rate_limit(request, max_requests=2, window_seconds=60)
    
    # Next request should raise HTTPException with custom message
    custom_message = "Custom rate limit message"
    with pytest.raises(HTTPException) as exc_info:
        check_rate_limit(request, max_requests=2, window_seconds=60, error_message=custom_message)
    
    assert exc_info.value.detail == custom_message
    assert exc_info.value.headers["Retry-After"] == "60"


def test_rate_limiter_partial_window_usage():
    """Test rate limiter behavior with partial window usage."""
    limiter = InMemoryRateLimiter()
    
    # Make 2 requests
    assert limiter.is_allowed("***********", max_requests=3, window_seconds=2) is True
    assert limiter.is_allowed("***********", max_requests=3, window_seconds=2) is True
    
    # Wait half the window
    time.sleep(1)
    
    # Should still be able to make 1 more request
    assert limiter.is_allowed("***********", max_requests=3, window_seconds=2) is True
    
    # Now should be blocked
    assert limiter.is_allowed("***********", max_requests=3, window_seconds=2) is False
    
    # Wait for first requests to expire
    time.sleep(1.1)
    
    # Should be able to make requests again (first 2 expired)
    assert limiter.is_allowed("***********", max_requests=3, window_seconds=2) is True
