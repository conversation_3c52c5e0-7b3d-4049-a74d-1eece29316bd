import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import get_password_hash
from app.models.user import User, UserRole
from app.models.wallet import FiatWallet, CryptoWallet


@pytest.fixture
async def test_user_with_wallets(async_session: AsyncSession):
    """Create a test user with wallets."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Withdraw",
        last_name="Test",
        is_active=True,
        role=UserRole.USER,
        fiat_withdraw_limit=1000.0,
        crypto_withdraw_limit=1.0,
        fiat_withdraw_used=0.0,
        crypto_withdraw_used=0.0,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    # Create wallets
    fiat_wallet = FiatWallet(
        user_id=user.id,
        balance=2000.0,
        currency="USD",
    )
    crypto_wallet = CryptoWallet(
        user_id=user.id,
        balance=2.0,
        currency="BTC",
        wallet_address="test-btc-address",
    )
    async_session.add(fiat_wallet)
    async_session.add(crypto_wallet)
    await async_session.commit()
    
    return user


@pytest.mark.asyncio
async def test_withdraw_limit_tracking_fiat(client: TestClient, test_user_with_wallets: User):
    """Test that fiat withdraw limits are properly tracked."""
    # Login
    login_data = {"username": test_user_with_wallets.email, "password": "password"}
    response = client.post("/api/v1/auth/login", data=login_data)
    assert response.status_code == 200
    token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}

    # Check initial limits
    response = client.get("/api/v1/users/me/withdraw-limits", headers=headers)
    assert response.status_code == 200
    limits = response.json()
    assert limits["fiat_limit"] == 1000.0
    assert limits["fiat_used"] == 0.0
    assert limits["fiat_available"] == 1000.0

    # Make a withdrawal
    withdrawal_data = {
        "amount": 300.0,
        "wallet_type": "fiat",
        "withdrawal_method": "wire_transfer",
        "description": "Test withdrawal"
    }
    response = client.post("/api/v1/transactions/withdraw", json=withdrawal_data, headers=headers)
    assert response.status_code == 200

    # Check updated limits
    response = client.get("/api/v1/users/me/withdraw-limits", headers=headers)
    assert response.status_code == 200
    limits = response.json()
    assert limits["fiat_used"] == 300.0
    assert limits["fiat_available"] == 700.0

    # Try to withdraw more than available limit
    withdrawal_data["amount"] = 800.0  # Would exceed remaining limit of 700
    response = client.post("/api/v1/transactions/withdraw", json=withdrawal_data, headers=headers)
    assert response.status_code == 400
    assert "exceeds available limit" in response.json()["detail"]


@pytest.mark.asyncio
async def test_withdraw_limit_tracking_crypto(client: TestClient, test_user_with_wallets: User):
    """Test that crypto withdraw limits are properly tracked."""
    # Login
    login_data = {"username": test_user_with_wallets.email, "password": "password"}
    response = client.post("/api/v1/auth/login", data=login_data)
    assert response.status_code == 200
    token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}

    # Make a crypto withdrawal
    withdrawal_data = {
        "amount": 0.3,
        "wallet_type": "crypto",
        "withdrawal_method": "crypto",
        "description": "Test crypto withdrawal"
    }
    response = client.post("/api/v1/transactions/withdraw", json=withdrawal_data, headers=headers)
    assert response.status_code == 200

    # Check updated limits
    response = client.get("/api/v1/users/me/withdraw-limits", headers=headers)
    assert response.status_code == 200
    limits = response.json()
    assert limits["crypto_used"] == 0.3
    assert limits["crypto_available"] == 0.7

    # Try to withdraw more than available limit
    withdrawal_data["amount"] = 0.8  # Would exceed remaining limit of 0.7
    response = client.post("/api/v1/transactions/withdraw", json=withdrawal_data, headers=headers)
    assert response.status_code == 400
    assert "exceeds available limit" in response.json()["detail"]


@pytest.mark.asyncio
async def test_admin_reset_withdraw_usage(client: TestClient, test_user_with_wallets: User, admin_user: User):
    """Test that admin can reset withdraw usage."""
    # Login as admin
    login_data = {"username": admin_user.email, "password": "password"}
    response = client.post("/api/v1/auth/login", data=login_data)
    assert response.status_code == 200
    admin_token = response.json()["access_token"]
    admin_headers = {"Authorization": f"Bearer {admin_token}"}

    # First, make some withdrawals as the test user
    login_data = {"username": test_user_with_wallets.email, "password": "password"}
    response = client.post("/api/v1/auth/login", data=login_data)
    user_token = response.json()["access_token"]
    user_headers = {"Authorization": f"Bearer {user_token}"}

    # Make withdrawals
    withdrawal_data = {
        "amount": 500.0,
        "wallet_type": "fiat",
        "withdrawal_method": "wire_transfer",
        "description": "Test withdrawal"
    }
    client.post("/api/v1/transactions/withdraw", json=withdrawal_data, headers=user_headers)

    withdrawal_data = {
        "amount": 0.5,
        "wallet_type": "crypto",
        "withdrawal_method": "crypto",
        "description": "Test crypto withdrawal"
    }
    client.post("/api/v1/transactions/withdraw", json=withdrawal_data, headers=user_headers)

    # Verify usage is tracked
    response = client.get("/api/v1/users/me/withdraw-limits", headers=user_headers)
    limits = response.json()
    assert limits["fiat_used"] == 500.0
    assert limits["crypto_used"] == 0.5

    # Admin resets fiat usage
    response = client.put(
        f"/api/v1/users/{test_user_with_wallets.id}/reset-withdraw-usage?reset_fiat=true",
        headers=admin_headers
    )
    assert response.status_code == 200

    # Check that fiat usage is reset but crypto is not
    response = client.get("/api/v1/users/me/withdraw-limits", headers=user_headers)
    limits = response.json()
    assert limits["fiat_used"] == 0.0
    assert limits["crypto_used"] == 0.5  # Should remain unchanged

    # Admin resets crypto usage
    response = client.put(
        f"/api/v1/users/{test_user_with_wallets.id}/reset-withdraw-usage?reset_crypto=true",
        headers=admin_headers
    )
    assert response.status_code == 200

    # Check that crypto usage is now reset
    response = client.get("/api/v1/users/me/withdraw-limits", headers=user_headers)
    limits = response.json()
    assert limits["fiat_used"] == 0.0
    assert limits["crypto_used"] == 0.0
