import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import get_password_hash
from app.models.user import User, UserRole
from app.models.wallet import CryptoWallet
from app.models.wallet_config import WalletConfig


@pytest.fixture
async def admin_user(async_session: AsyncSession):
    """Create an admin user."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Admin",
        last_name="User",
        is_active=True,
        role=UserRole.ADMIN,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)
    return user


@pytest.fixture
async def regular_user(async_session: AsyncSession):
    """Create a regular user."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Regular",
        last_name="User",
        is_active=True,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)
    return user


@pytest.fixture
async def user_with_crypto_wallet(async_session: AsyncSession, regular_user: User):
    """Create a user with a crypto wallet."""
    crypto_wallet = CryptoWallet(
        user_id=regular_user.id,
        balance=1.0,
        currency="BTC",
        wallet_address="btc-original-address",
    )
    async_session.add(crypto_wallet)
    await async_session.commit()
    return regular_user


@pytest.fixture
async def admin_headers(client: TestClient, admin_user: User):
    """Get authentication headers for the admin user."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": admin_user.email, "password": "password"},
    )
    assert response.status_code == 200
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def user_headers(client: TestClient, regular_user: User):
    """Get authentication headers for the regular user."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": regular_user.email, "password": "password"},
    )
    assert response.status_code == 200
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.mark.asyncio
async def test_create_wallet_config(client: TestClient, admin_user: User, admin_headers: dict):
    """Test creating a wallet configuration."""
    config_data = {
        "config_type": "default_crypto_address",
        "currency": "BTC",
        "default_address": "btc-default-123",
        "description": "Default BTC address for new users",
        "is_active": True,
        "created_by": admin_user.id,
        "updated_by": admin_user.id,
    }
    response = client.post("/api/v1/admin/wallet-configs", json=config_data, headers=admin_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["currency"] == "BTC"
    assert data["default_address"] == "btc-default-123"
    assert data["is_active"] is True


@pytest.mark.asyncio
async def test_get_wallet_configs(client: TestClient, admin_user: User, admin_headers: dict, async_session: AsyncSession):
    """Test getting all wallet configurations."""
    # Create a test config
    config = WalletConfig(
        config_type="default_crypto_address",
        currency="ETH",
        default_address="eth-default-456",
        description="Default ETH address",
        is_active=True,
        created_by=admin_user.id,
        updated_by=admin_user.id,
    )
    async_session.add(config)
    await async_session.commit()

    response = client.get("/api/v1/admin/wallet-configs", headers=admin_headers)
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) >= 1
    assert any(item["currency"] == "ETH" for item in data)


@pytest.mark.asyncio
async def test_update_wallet_config(client: TestClient, admin_user: User, admin_headers: dict, async_session: AsyncSession):
    """Test updating a wallet configuration."""
    # Create a test config
    config = WalletConfig(
        config_type="default_crypto_address",
        currency="LTC",
        default_address="ltc-old-address",
        description="Old description",
        is_active=True,
        created_by=admin_user.id,
        updated_by=admin_user.id,
    )
    async_session.add(config)
    await async_session.commit()
    await async_session.refresh(config)

    update_data = {
        "default_address": "ltc-new-address",
        "description": "Updated description",
        "updated_by": admin_user.id,
    }
    response = client.put(f"/api/v1/admin/wallet-configs/{config.id}", json=update_data, headers=admin_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["default_address"] == "ltc-new-address"
    assert data["description"] == "Updated description"


@pytest.mark.asyncio
async def test_deactivate_wallet_config(client: TestClient, admin_user: User, admin_headers: dict, async_session: AsyncSession):
    """Test deactivating a wallet configuration."""
    # Create a test config
    config = WalletConfig(
        config_type="default_crypto_address",
        currency="DOGE",
        default_address="doge-address",
        description="DOGE config",
        is_active=True,
        created_by=admin_user.id,
        updated_by=admin_user.id,
    )
    async_session.add(config)
    await async_session.commit()
    await async_session.refresh(config)

    response = client.delete(f"/api/v1/admin/wallet-configs/{config.id}", headers=admin_headers)
    assert response.status_code == 200
    data = response.json()
    assert "deactivated successfully" in data["message"]


@pytest.mark.asyncio
async def test_get_wallet_config_by_currency(client: TestClient, admin_user: User, admin_headers: dict, async_session: AsyncSession):
    """Test getting wallet configuration by currency."""
    # Create a test config
    config = WalletConfig(
        config_type="default_crypto_address",
        currency="ADA",
        default_address="ada-address",
        description="ADA config",
        is_active=True,
        created_by=admin_user.id,
        updated_by=admin_user.id,
    )
    async_session.add(config)
    await async_session.commit()

    response = client.get("/api/v1/admin/wallet-configs/currency/ADA", headers=admin_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["currency"] == "ADA"
    assert data["default_address"] == "ada-address"


@pytest.mark.asyncio
async def test_update_user_crypto_wallet_address(client: TestClient, user_with_crypto_wallet: User, admin_headers: dict):
    """Test updating user's crypto wallet address."""
    address_data = {
        "wallet_address": "btc-new-address-123"
    }
    response = client.put(
        f"/api/v1/admin/users/{user_with_crypto_wallet.id}/crypto-wallet-address",
        json=address_data,
        headers=admin_headers
    )
    assert response.status_code == 200
    data = response.json()
    assert "updated successfully" in data["message"]
    assert data["new_address"] == "btc-new-address-123"


@pytest.mark.asyncio
async def test_update_user_withdraw_limits(client: TestClient, regular_user: User, admin_headers: dict):
    """Test updating user withdraw limits."""
    limits_data = {
        "fiat_withdraw_limit": 5000.0,
        "crypto_withdraw_limit": 0.5,
    }
    response = client.put(
        f"/api/v1/users/{regular_user.id}/withdraw-limits",
        json=limits_data,
        headers=admin_headers
    )
    assert response.status_code == 200
    data = response.json()
    assert data["fiat_withdraw_limit"] == 5000.0
    assert data["crypto_withdraw_limit"] == 0.5


@pytest.mark.asyncio
async def test_admin_endpoints_require_admin_role(client: TestClient, user_headers: dict):
    """Test that admin endpoints require admin role."""
    # Test wallet config endpoints
    response = client.get("/api/v1/admin/wallet-configs", headers=user_headers)
    assert response.status_code == 403

    config_data = {
        "config_type": "default_crypto_address",
        "currency": "BTC",
        "default_address": "btc-address",
        "created_by": "user-id",
        "updated_by": "user-id",
    }
    response = client.post("/api/v1/admin/wallet-configs", json=config_data, headers=user_headers)
    assert response.status_code == 403

    # Test user management endpoints
    limits_data = {"fiat_withdraw_limit": 1000.0}
    response = client.put("/api/v1/users/some-id/withdraw-limits", json=limits_data, headers=user_headers)
    assert response.status_code == 403


@pytest.mark.asyncio
async def test_admin_endpoints_require_auth(client: TestClient):
    """Test that admin endpoints require authentication."""
    response = client.get("/api/v1/admin/wallet-configs")
    assert response.status_code == 401

    response = client.post("/api/v1/admin/wallet-configs", json={})
    assert response.status_code == 401


@pytest.mark.asyncio
async def test_create_duplicate_wallet_config(client: TestClient, admin_user: User, admin_headers: dict, async_session: AsyncSession):
    """Test creating duplicate wallet configuration fails."""
    # Create first config
    config = WalletConfig(
        config_type="default_crypto_address",
        currency="BTC",
        default_address="btc-first",
        description="First BTC config",
        is_active=True,
        created_by=admin_user.id,
        updated_by=admin_user.id,
    )
    async_session.add(config)
    await async_session.commit()

    # Try to create duplicate
    config_data = {
        "config_type": "default_crypto_address",
        "currency": "BTC",
        "default_address": "btc-second",
        "description": "Second BTC config",
        "is_active": True,
        "created_by": admin_user.id,
        "updated_by": admin_user.id,
    }
    response = client.post("/api/v1/admin/wallet-configs", json=config_data, headers=admin_headers)
    assert response.status_code == 400
    assert "already exists" in response.json()["detail"]
