import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from unittest.mock import patch

from app.crud import user as user_crud
from app.crud import user_profile as user_profile_crud
from app.crud import fiat_wallet as fiat_wallet_crud
from app.crud import crypto_wallet as crypto_wallet_crud


@pytest.mark.asyncio
async def test_public_registration_success(client: TestClient, async_session: AsyncSession):
    """Test successful public registration."""
    # Mock the rate limiter to allow the request
    from unittest.mock import patch
    with patch('app.utils.rate_limiter.rate_limiter.is_allowed', return_value=True):
        registration_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "first_name": "New",
            "last_name": "User"
        }

        response = client.post("/api/v1/auth/register", json=registration_data)
    assert response.status_code == 200

    data = response.json()
    assert data["message"] == "Registration successful. Your account is pending admin approval."
    assert data["email"] == "<EMAIL>"
    assert data["is_active"] is False  # Should be inactive by default

    # Verify user was created in database
    user = await user_crud.get_by_email(async_session, email="<EMAIL>")
    assert user is not None
    assert user.is_active is False
    assert user.first_name == "New"
    assert user.last_name == "User"

    # Verify profile was created
    profile = await user_profile_crud.get_by_user_id(async_session, user_id=user.id)
    assert profile is not None
    assert profile.user_id == user.id

    # Verify wallets were created
    fiat_wallet = await fiat_wallet_crud.get_by_user_id(async_session, user_id=user.id)
    assert fiat_wallet is not None
    assert fiat_wallet.currency == "USD"
    assert fiat_wallet.balance == 0.0

    crypto_wallet = await crypto_wallet_crud.get_by_user_id(async_session, user_id=user.id)
    assert crypto_wallet is not None
    assert crypto_wallet.currency == "BTC"
    assert crypto_wallet.balance == 0.0
    assert crypto_wallet.wallet_address.startswith("btc-")


@pytest.mark.asyncio
async def test_public_registration_duplicate_email(client: TestClient, async_session: AsyncSession):
    """Test registration with duplicate email."""
    # Mock the rate limiter to allow the requests
    from unittest.mock import patch
    with patch('app.utils.rate_limiter.rate_limiter.is_allowed', return_value=True):
        # First registration
        registration_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "first_name": "First",
            "last_name": "User"
        }
        response = client.post("/api/v1/auth/register", json=registration_data)
        assert response.status_code == 200

        # Second registration with same email
        registration_data["first_name"] = "Second"
        response = client.post("/api/v1/auth/register", json=registration_data)
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]


@pytest.mark.asyncio
async def test_registration_rate_limiting(client: TestClient):
    """Test rate limiting on registration endpoint."""
    registration_data = {
        "email": "ratelimit{}@example.com",
        "password": "password123",
        "first_name": "Rate",
        "last_name": "Limit"
    }

    # Mock the rate limiter to simulate multiple requests from same IP
    with patch('app.utils.rate_limiter.rate_limiter.is_allowed') as mock_is_allowed:
        # First 3 requests should be allowed
        mock_is_allowed.return_value = True

        for i in range(3):
            registration_data["email"] = f"ratelimit{i}@example.com"
            response = client.post("/api/v1/auth/register", json=registration_data)
            assert response.status_code == 200

        # 4th request should be rate limited
        mock_is_allowed.return_value = False
        registration_data["email"] = "<EMAIL>"
        response = client.post("/api/v1/auth/register", json=registration_data)
        assert response.status_code == 429
        assert "rate limit exceeded" in response.json()["detail"].lower()


@pytest.mark.asyncio
async def test_inactive_user_cannot_login(client: TestClient, async_session: AsyncSession):
    """Test that inactive users cannot login."""
    # Mock the rate limiter to allow the request
    from unittest.mock import patch
    with patch('app.utils.rate_limiter.rate_limiter.is_allowed', return_value=True):
        # Register a new user (will be inactive)
        registration_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "first_name": "Inactive",
            "last_name": "User"
        }
        response = client.post("/api/v1/auth/register", json=registration_data)
        assert response.status_code == 200

    # Try to login
    login_data = {"username": "<EMAIL>", "password": "password123"}
    response = client.post("/api/v1/auth/login", data=login_data)
    assert response.status_code == 400
    assert "Inactive user" in response.json()["detail"]


@pytest.mark.asyncio
async def test_admin_created_user_is_active(client: TestClient, admin_user, async_session: AsyncSession):
    """Test that admin-created users are active by default."""
    # Login as admin
    login_data = {"username": admin_user.email, "password": "password"}
    response = client.post("/api/v1/auth/login", data=login_data)
    assert response.status_code == 200
    admin_token = response.json()["access_token"]
    admin_headers = {"Authorization": f"Bearer {admin_token}"}

    # Create user as admin
    user_data = {
        "email": "<EMAIL>",
        "password": "password123",
        "first_name": "Admin",
        "last_name": "Created",
        "is_active": False  # Even if we set this to False, it should be overridden to True
    }
    response = client.post("/api/v1/users/", json=user_data, headers=admin_headers)
    assert response.status_code == 200

    created_user = response.json()
    assert created_user["is_active"] is True  # Should be active

    # Verify user can login
    login_data = {"username": "<EMAIL>", "password": "password123"}
    response = client.post("/api/v1/auth/login", data=login_data)
    assert response.status_code == 200

    # Verify profile and wallets were created
    user = await user_crud.get_by_email(async_session, email="<EMAIL>")

    profile = await user_profile_crud.get_by_user_id(async_session, user_id=user.id)
    assert profile is not None

    fiat_wallet = await fiat_wallet_crud.get_by_user_id(async_session, user_id=user.id)
    assert fiat_wallet is not None

    crypto_wallet = await crypto_wallet_crud.get_by_user_id(async_session, user_id=user.id)
    assert crypto_wallet is not None


@pytest.mark.asyncio
async def test_admin_password_reset(client: TestClient, admin_user, async_session: AsyncSession):
    """Test admin password reset functionality."""
    # Login as admin
    login_data = {"username": admin_user.email, "password": "password"}
    response = client.post("/api/v1/auth/login", data=login_data)
    assert response.status_code == 200
    admin_token = response.json()["access_token"]
    admin_headers = {"Authorization": f"Bearer {admin_token}"}

    # Create a test user first
    from unittest.mock import patch
    with patch('app.utils.rate_limiter.rate_limiter.is_allowed', return_value=True):
        user_data = {
            "email": "<EMAIL>",
            "password": "oldpassword123",
            "first_name": "Password",
            "last_name": "Reset"
        }
        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 200
        user_id = response.json()["user_id"]

    # Admin resets password to custom password
    reset_data = {"new_password": "newpassword123"}
    response = client.put(
        f"/api/v1/users/{user_id}/reset-password",
        json=reset_data,
        headers=admin_headers
    )
    assert response.status_code == 200

    # Admin resets password to default (no password provided)
    reset_data = {}
    response = client.put(
        f"/api/v1/users/{user_id}/reset-password",
        json=reset_data,
        headers=admin_headers
    )
    assert response.status_code == 200


@pytest.mark.asyncio
async def test_registration_validation(client: TestClient):
    """Test registration input validation."""
    # Mock the rate limiter to allow the requests
    from unittest.mock import patch
    with patch('app.utils.rate_limiter.rate_limiter.is_allowed', return_value=True):
        # Test missing required fields
        response = client.post("/api/v1/auth/register", json={})
        assert response.status_code == 422

        # Test invalid email
        registration_data = {
            "email": "invalid-email",
            "password": "password123",
            "first_name": "Test",
            "last_name": "User"
        }
        response = client.post("/api/v1/auth/register", json=registration_data)
        assert response.status_code == 422

        # Test short password
        registration_data = {
            "email": "<EMAIL>",
            "password": "short",
            "first_name": "Test",
            "last_name": "User"
        }
        response = client.post("/api/v1/auth/register", json=registration_data)
        assert response.status_code == 422
