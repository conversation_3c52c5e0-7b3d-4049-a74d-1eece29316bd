import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import get_password_hash
from app.models.user import User, UserRole
from app.models.wallet import FiatWallet, CryptoWallet


@pytest.fixture
async def test_user_with_wallets(async_session: AsyncSession):
    """Create a test user with wallets in the database."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Wallet",
        last_name="User",
        is_active=True,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)
    
    # Create wallets
    fiat_wallet = FiatWallet(
        user_id=user.id,
        balance=1000.0,
        currency="USD",
    )
    crypto_wallet = CryptoWallet(
        user_id=user.id,
        balance=0.5,
        currency="BTC",
        wallet_address="btc-test-address",
    )
    async_session.add(fiat_wallet)
    async_session.add(crypto_wallet)
    await async_session.commit()
    
    return user


@pytest.fixture
async def auth_headers(client: TestClient, test_user_with_wallets: User):
    """Get authentication headers for the test user."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": test_user_with_wallets.email, "password": "password"},
    )
    assert response.status_code == 200
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.mark.asyncio
async def test_get_fiat_wallet(client: TestClient, test_user_with_wallets: User, auth_headers: dict):
    """Test getting user's fiat wallet."""
    response = client.get("/api/v1/wallets/fiat/me", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["balance"] == 1000.0
    assert data["currency"] == "USD"
    assert data["user_id"] == test_user_with_wallets.id


@pytest.mark.asyncio
async def test_get_crypto_wallet(client: TestClient, test_user_with_wallets: User, auth_headers: dict):
    """Test getting user's crypto wallet."""
    response = client.get("/api/v1/wallets/crypto/me", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["balance"] == 0.5
    assert data["currency"] == "BTC"
    assert data["wallet_address"] == "btc-test-address"
    assert data["user_id"] == test_user_with_wallets.id


@pytest.mark.asyncio
async def test_get_wallet_without_auth(client: TestClient):
    """Test getting wallet without authentication."""
    response = client.get("/api/v1/wallets/fiat/me")
    assert response.status_code == 401


@pytest.mark.asyncio
async def test_get_user_with_wallet_balances(client: TestClient, test_user_with_wallets: User, auth_headers: dict):
    """Test getting user profile with wallet balances."""
    response = client.get("/api/v1/users/me", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == test_user_with_wallets.email
    assert data["fiat_wallet_balance"] == 1000.0
    assert data["crypto_wallet_balance"] == 0.5
