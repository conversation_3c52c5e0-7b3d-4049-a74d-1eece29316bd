import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.ext.asyncio import AsyncSession
import io

from app.core.security import get_password_hash
from app.models.user import User, UserRole
from app.models.user_profile import UserProfile, KYCStatus


@pytest.fixture
async def test_user(async_session: AsyncSession):
    """Create a test user in the database."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Profile",
        last_name="Test",
        is_active=True,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)
    return user


@pytest.fixture
async def admin_user(async_session: AsyncSession):
    """Create an admin user."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Admin",
        last_name="User",
        is_active=True,
        role=UserRole.ADMIN,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)
    return user


@pytest.fixture
async def user_with_profile(async_session: AsyncSession, test_user: User):
    """Create a user with a profile."""
    profile = UserProfile(
        user_id=test_user.id,
        phone_number="+1234567890",
        address_line_1="123 Test St",
        city="Test City",
        country="Test Country",
        kyc_status=KYCStatus.PENDING,
    )
    async_session.add(profile)
    await async_session.commit()
    await async_session.refresh(profile)
    return test_user


@pytest.fixture
async def auth_headers(client: TestClient, test_user: User):
    """Get authentication headers for the test user."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "password"},
    )
    assert response.status_code == 200
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def admin_headers(client: TestClient, admin_user: User):
    """Get authentication headers for the admin user."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": admin_user.email, "password": "password"},
    )
    assert response.status_code == 200
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.mark.asyncio
async def test_get_my_profile_creates_if_not_exists(client: TestClient, test_user: User, auth_headers: dict):
    """Test getting user's profile creates one if it doesn't exist."""
    response = client.get("/api/v1/profiles/me", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["user_id"] == test_user.id
    assert data["kyc_status"] == "pending"


@pytest.mark.asyncio
async def test_update_my_profile(client: TestClient, test_user: User, auth_headers: dict):
    """Test updating user's profile."""
    profile_data = {
        "phone_number": "+1234567890",
        "address_line_1": "123 Updated St",
        "city": "Updated City",
        "country": "Updated Country",
    }
    response = client.put("/api/v1/profiles/me", json=profile_data, headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["phone_number"] == "+1234567890"
    assert data["address_line_1"] == "123 Updated St"
    assert data["city"] == "Updated City"


@pytest.mark.asyncio
async def test_upload_avatar_success(client: TestClient, test_user: User, auth_headers: dict):
    """Test successful avatar upload."""
    # Create a small test image (PNG format)
    image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
    
    files = {"file": ("test.png", io.BytesIO(image_data), "image/png")}
    response = client.post("/api/v1/profiles/me/avatar", files=files, headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "size" in data


@pytest.mark.asyncio
async def test_upload_avatar_invalid_format(client: TestClient, test_user: User, auth_headers: dict):
    """Test avatar upload with invalid format."""
    files = {"file": ("test.txt", io.BytesIO(b"not an image"), "text/plain")}
    response = client.post("/api/v1/profiles/me/avatar", files=files, headers=auth_headers)
    assert response.status_code == 400
    assert "JPEG and PNG" in response.json()["detail"]


@pytest.mark.asyncio
async def test_submit_kyc(client: TestClient, user_with_profile: User, auth_headers: dict):
    """Test submitting KYC for review."""
    response = client.post("/api/v1/profiles/me/submit-kyc", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["kyc_status"] == "under_review"
    assert data["kyc_submitted_at"] is not None


@pytest.mark.asyncio
async def test_admin_get_user_profile(client: TestClient, user_with_profile: User, admin_headers: dict):
    """Test admin getting user profile."""
    response = client.get(f"/api/v1/profiles/{user_with_profile.id}", headers=admin_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["user_id"] == user_with_profile.id
    assert data["phone_number"] == "+1234567890"


@pytest.mark.asyncio
async def test_admin_review_kyc_approve(client: TestClient, user_with_profile: User, admin_headers: dict):
    """Test admin approving KYC."""
    review_data = {
        "kyc_status": "approved"
    }
    response = client.put(f"/api/v1/profiles/{user_with_profile.id}/kyc-review", json=review_data, headers=admin_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["kyc_status"] == "approved"
    assert data["kyc_reviewed_at"] is not None


@pytest.mark.asyncio
async def test_admin_review_kyc_reject(client: TestClient, user_with_profile: User, admin_headers: dict):
    """Test admin rejecting KYC."""
    review_data = {
        "kyc_status": "rejected",
        "kyc_rejection_reason": "Invalid documents"
    }
    response = client.put(f"/api/v1/profiles/{user_with_profile.id}/kyc-review", json=review_data, headers=admin_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["kyc_status"] == "rejected"
    assert data["kyc_rejection_reason"] == "Invalid documents"


@pytest.mark.asyncio
async def test_profile_endpoints_require_auth(client: TestClient):
    """Test that profile endpoints require authentication."""
    response = client.get("/api/v1/profiles/me")
    assert response.status_code == 401
    
    response = client.put("/api/v1/profiles/me", json={})
    assert response.status_code == 401
    
    response = client.post("/api/v1/profiles/me/submit-kyc")
    assert response.status_code == 401


@pytest.mark.asyncio
async def test_admin_endpoints_require_admin_role(client: TestClient, test_user: User, auth_headers: dict):
    """Test that admin endpoints require admin role."""
    response = client.get(f"/api/v1/profiles/{test_user.id}", headers=auth_headers)
    assert response.status_code == 403
    
    review_data = {"kyc_status": "approved"}
    response = client.put(f"/api/v1/profiles/{test_user.id}/kyc-review", json=review_data, headers=auth_headers)
    assert response.status_code == 403
