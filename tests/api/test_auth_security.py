import pytest
import time
from fastapi.testclient import Test<PERSON>lient
from jose import jwt
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.security import get_password_hash
from app.models.user import User, UserRole


@pytest.fixture
async def test_user(async_session: AsyncSession):
    """Create a test user in the database."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Auth",
        last_name="Test",
        is_active=True,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)
    return user


@pytest.fixture
async def inactive_user(async_session: AsyncSession):
    """Create an inactive test user in the database."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Inactive",
        last_name="User",
        is_active=False,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)
    return user


@pytest.fixture
async def admin_user(async_session: AsyncSession):
    """Create an admin test user in the database."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Admin",
        last_name="User",
        is_active=True,
        role=UserRole.ADMIN,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)
    return user


@pytest.mark.asyncio
async def test_login_success(client: TestClient, test_user: User):
    """Test successful login."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "password"},
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data
    assert data["token_type"] == "bearer"

    # Verify the access token
    payload = jwt.decode(
        data["access_token"],
        settings.SECRET_KEY,
        algorithms=[settings.ALGORITHM],
    )
    assert payload["sub"] == str(test_user.id)
    assert payload["type"] == "access"

    # Verify the refresh token
    payload = jwt.decode(
        data["refresh_token"],
        settings.SECRET_KEY,
        algorithms=[settings.ALGORITHM],
    )
    assert payload["sub"] == str(test_user.id)
    assert payload["type"] == "refresh"


@pytest.mark.asyncio
async def test_login_wrong_password(client: TestClient, test_user: User):
    """Test login with wrong password."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "wrong_password"},
    )
    assert response.status_code == 401
    assert "detail" in response.json()


@pytest.mark.asyncio
async def test_login_inactive_user(client: TestClient, inactive_user: User):
    """Test login with inactive user."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": inactive_user.email, "password": "password"},
    )
    assert response.status_code == 400
    assert "detail" in response.json()


@pytest.mark.asyncio
async def test_refresh_token(client: TestClient, test_user: User):
    """Test refreshing a token."""
    import time

    # First, login to get tokens
    login_response = client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "password"},
    )
    assert login_response.status_code == 200
    tokens = login_response.json()

    # Wait a moment to ensure different timestamps
    time.sleep(1)

    # Then, use the refresh token
    refresh_response = client.post(
        "/api/v1/auth/refresh",
        json={"refresh_token": tokens["refresh_token"]},
    )
    assert refresh_response.status_code == 200
    new_tokens = refresh_response.json()
    assert "access_token" in new_tokens
    assert "refresh_token" in new_tokens
    assert new_tokens["token_type"] == "bearer"

    # Verify the new tokens are different (they should be due to different timestamps)
    assert new_tokens["access_token"] != tokens["access_token"]
    assert new_tokens["refresh_token"] != tokens["refresh_token"]


@pytest.mark.asyncio
async def test_refresh_with_access_token(client: TestClient, test_user: User):
    """Test refreshing with an access token instead of a refresh token."""
    # First, login to get tokens
    login_response = client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "password"},
    )
    assert login_response.status_code == 200
    tokens = login_response.json()

    # Then, try to use the access token as a refresh token
    refresh_response = client.post(
        "/api/v1/auth/refresh",
        json={"refresh_token": tokens["access_token"]},
    )
    assert refresh_response.status_code == 401
    assert "detail" in refresh_response.json()


@pytest.mark.asyncio
async def test_token_expiration(client: TestClient, test_user: User, monkeypatch):
    """Test token expiration."""
    # Set a very short expiration time for testing
    monkeypatch.setattr(settings, "ACCESS_TOKEN_EXPIRE_MINUTES", 0.05)  # 3 seconds

    # Login to get tokens
    login_response = client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "password"},
    )
    assert login_response.status_code == 200
    tokens = login_response.json()

    # Test the token works initially
    test_response = client.post(
        "/api/v1/auth/test-token",
        headers={"Authorization": f"Bearer {tokens['access_token']}"},
    )
    assert test_response.status_code == 200

    # Wait for the token to expire
    time.sleep(4)

    # Test the token no longer works
    expired_response = client.post(
        "/api/v1/auth/test-token",
        headers={"Authorization": f"Bearer {tokens['access_token']}"},
    )
    assert expired_response.status_code == 401


@pytest.mark.asyncio
async def test_register_user(client: TestClient):
    """Test registering a new user."""
    user_data = {
        "email": "<EMAIL>",
        "password": "password123",
        "first_name": "New",
        "last_name": "User",
    }
    response = client.post(
        "/api/v1/auth/register",
        json=user_data,
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data
    assert data["token_type"] == "bearer"


@pytest.mark.asyncio
async def test_register_existing_email(client: TestClient, test_user: User):
    """Test registering with an existing email."""
    user_data = {
        "email": test_user.email,
        "password": "password123",
        "first_name": "Duplicate",
        "last_name": "User",
    }
    response = client.post(
        "/api/v1/auth/register",
        json=user_data,
    )
    assert response.status_code == 400
    assert "detail" in response.json()
