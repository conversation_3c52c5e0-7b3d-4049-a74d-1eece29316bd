import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import get_password_hash
from app.models.user import User, UserRole
from app.models.wallet import FiatWallet, CryptoWallet


@pytest.fixture
async def test_user_with_wallets(async_session: AsyncSession):
    """Create a test user with wallets in the database."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Transaction",
        last_name="User",
        is_active=True,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    # Create wallets with some balance
    fiat_wallet = FiatWallet(
        user_id=user.id,
        balance=1000.0,
        currency="USD",
    )
    crypto_wallet = CryptoWallet(
        user_id=user.id,
        balance=0.5,
        currency="BTC",
        wallet_address="btc-test-address",
    )
    async_session.add(fiat_wallet)
    async_session.add(crypto_wallet)
    await async_session.commit()

    return user


@pytest.fixture
async def admin_user(async_session: AsyncSession):
    """Create an admin user with wallets."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Admin",
        last_name="User",
        is_active=True,
        role=UserRole.ADMIN,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    # Create wallets for admin user
    fiat_wallet = FiatWallet(
        user_id=user.id,
        balance=0.0,
        currency="USD",
    )
    crypto_wallet = CryptoWallet(
        user_id=user.id,
        balance=0.0,
        currency="BTC",
        wallet_address="btc-admin-address",
    )
    async_session.add(fiat_wallet)
    async_session.add(crypto_wallet)
    await async_session.commit()

    return user


@pytest.fixture
async def auth_headers(client: TestClient, test_user_with_wallets: User):
    """Get authentication headers for the test user."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": test_user_with_wallets.email, "password": "password"},
    )
    assert response.status_code == 200
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def admin_headers(client: TestClient, admin_user: User):
    """Get authentication headers for the admin user."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": admin_user.email, "password": "password"},
    )
    assert response.status_code == 200
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.mark.asyncio
async def test_get_user_transactions(client: TestClient, test_user_with_wallets: User, auth_headers: dict):
    """Test getting user's transactions."""
    response = client.get("/api/v1/transactions/me", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)


@pytest.mark.asyncio
async def test_create_withdrawal_fiat(client: TestClient, test_user_with_wallets: User, auth_headers: dict):
    """Test creating a fiat withdrawal."""
    withdrawal_data = {
        "amount": 100.0,
        "wallet_type": "fiat",
        "withdrawal_method": "wire_transfer",
        "description": "Test withdrawal"
    }
    response = client.post("/api/v1/transactions/withdraw", json=withdrawal_data, headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["transaction_type"] == "withdrawal"
    assert data["amount"] == 100.0
    assert data["source_wallet_type"] == "fiat"
    assert data["withdrawal_method"] == "wire_transfer"
    assert data["status"] == "completed"


@pytest.mark.asyncio
async def test_create_withdrawal_insufficient_balance(client: TestClient, test_user_with_wallets: User, auth_headers: dict):
    """Test creating a withdrawal with insufficient balance."""
    withdrawal_data = {
        "amount": 2000.0,  # More than the 1000.0 balance
        "wallet_type": "fiat",
        "withdrawal_method": "wire_transfer",
        "description": "Test withdrawal"
    }
    response = client.post("/api/v1/transactions/withdraw", json=withdrawal_data, headers=auth_headers)
    assert response.status_code == 400
    assert "Insufficient" in response.json()["detail"]


@pytest.mark.asyncio
async def test_create_conversion_fiat_to_crypto(client: TestClient, test_user_with_wallets: User, auth_headers: dict):
    """Test creating a conversion from fiat to crypto."""
    conversion_data = {
        "amount": 100.0,
        "source_wallet_type": "fiat",
        "destination_wallet_type": "crypto",
        "description": "Test conversion"
    }
    response = client.post("/api/v1/transactions/convert", json=conversion_data, headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["transaction_type"] == "conversion"
    assert data["amount"] == 100.0
    assert data["source_wallet_type"] == "fiat"
    assert data["destination_wallet_type"] == "crypto"
    assert data["status"] == "completed"
    assert data["conversion_rate"] is not None


@pytest.mark.asyncio
async def test_create_conversion_crypto_to_fiat(client: TestClient, test_user_with_wallets: User, auth_headers: dict):
    """Test creating a conversion from crypto to fiat."""
    conversion_data = {
        "amount": 0.1,
        "source_wallet_type": "crypto",
        "destination_wallet_type": "fiat",
        "description": "Test conversion"
    }
    response = client.post("/api/v1/transactions/convert", json=conversion_data, headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["transaction_type"] == "conversion"
    assert data["amount"] == 0.1
    assert data["source_wallet_type"] == "crypto"
    assert data["destination_wallet_type"] == "fiat"
    assert data["status"] == "completed"
    assert data["conversion_rate"] is not None


@pytest.mark.asyncio
async def test_create_conversion_same_wallet_type(client: TestClient, test_user_with_wallets: User, auth_headers: dict):
    """Test creating a conversion with same source and destination wallet types."""
    conversion_data = {
        "amount": 100.0,
        "source_wallet_type": "fiat",
        "destination_wallet_type": "fiat",
        "description": "Test conversion"
    }
    response = client.post("/api/v1/transactions/convert", json=conversion_data, headers=auth_headers)
    assert response.status_code == 400
    assert "different" in response.json()["detail"]


@pytest.mark.asyncio
async def test_create_deposit_admin_only(client: TestClient, admin_headers: dict):
    """Test creating a deposit (admin only)."""
    deposit_data = {
        "amount": 500.0,
        "wallet_type": "fiat",
        "description": "Test deposit"
    }
    response = client.post("/api/v1/transactions/deposit", json=deposit_data, headers=admin_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["transaction_type"] == "deposit"
    assert data["amount"] == 500.0
    assert data["source_wallet_type"] == "fiat"
    assert data["status"] == "completed"


@pytest.mark.asyncio
async def test_create_deposit_non_admin(client: TestClient, auth_headers: dict):
    """Test creating a deposit as non-admin user."""
    deposit_data = {
        "amount": 500.0,
        "wallet_type": "fiat",
        "description": "Test deposit"
    }
    response = client.post("/api/v1/transactions/deposit", json=deposit_data, headers=auth_headers)
    assert response.status_code == 403


@pytest.mark.asyncio
async def test_get_transactions_without_auth(client: TestClient):
    """Test getting transactions without authentication."""
    response = client.get("/api/v1/transactions/me")
    assert response.status_code == 401


@pytest.mark.asyncio
async def test_withdrawal_exceeds_fiat_limit(client: TestClient, async_session: AsyncSession):
    """Test withdrawal that exceeds fiat withdraw limit."""
    # Create user with low fiat withdraw limit
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Limit",
        last_name="Test",
        is_active=True,
        role=UserRole.USER,
        fiat_withdraw_limit=50.0,  # Low limit
        crypto_withdraw_limit=1.0,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    # Create wallet with sufficient balance
    fiat_wallet = FiatWallet(
        user_id=user.id,
        balance=1000.0,
        currency="USD",
    )
    async_session.add(fiat_wallet)
    await async_session.commit()

    # Get auth headers
    response = client.post(
        "/api/v1/auth/login",
        data={"username": user.email, "password": "password"},
    )
    assert response.status_code == 200
    token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}

    # Try to withdraw more than limit
    withdrawal_data = {
        "amount": 100.0,  # Exceeds 50.0 limit
        "wallet_type": "fiat",
        "withdrawal_method": "wire_transfer",
        "description": "Test withdrawal exceeding limit"
    }
    response = client.post("/api/v1/transactions/withdraw", json=withdrawal_data, headers=headers)
    assert response.status_code == 400
    assert "exceeds available limit" in response.json()["detail"]


@pytest.mark.asyncio
async def test_withdrawal_exceeds_crypto_limit(client: TestClient, async_session: AsyncSession):
    """Test withdrawal that exceeds crypto withdraw limit."""
    # Create user with low crypto withdraw limit
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Crypto",
        last_name="Test",
        is_active=True,
        role=UserRole.USER,
        fiat_withdraw_limit=10000.0,
        crypto_withdraw_limit=0.1,  # Low limit
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    # Create wallet with sufficient balance
    crypto_wallet = CryptoWallet(
        user_id=user.id,
        balance=1.0,
        currency="BTC",
        wallet_address="btc-test-address",
    )
    async_session.add(crypto_wallet)
    await async_session.commit()

    # Get auth headers
    response = client.post(
        "/api/v1/auth/login",
        data={"username": user.email, "password": "password"},
    )
    assert response.status_code == 200
    token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}

    # Try to withdraw more than limit
    withdrawal_data = {
        "amount": 0.5,  # Exceeds 0.1 limit
        "wallet_type": "crypto",
        "withdrawal_method": "crypto",
        "description": "Test crypto withdrawal exceeding limit"
    }
    response = client.post("/api/v1/transactions/withdraw", json=withdrawal_data, headers=headers)
    assert response.status_code == 400
    assert "exceeds available limit" in response.json()["detail"]
