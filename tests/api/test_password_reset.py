import pytest
from datetime import datetime, timedelta
from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.security import get_password_hash, verify_password
from app.crud import password_reset as password_reset_crud
from app.models.password_reset import PasswordReset
from app.models.user import User, UserRole


@pytest.fixture
async def test_user(async_session: AsyncSession):
    """Create a test user in the database."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Reset",
        last_name="Test",
        is_active=True,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)
    return user


@pytest.fixture
async def test_reset_token(async_session: AsyncSession, test_user: User):
    """Create a test password reset token."""
    expires_at = datetime.now() + timedelta(minutes=30)
    reset_token = PasswordReset(
        user_id=test_user.id,
        token="test-token",
        expires_at=expires_at,
    )
    async_session.add(reset_token)
    await async_session.commit()
    await async_session.refresh(reset_token)
    return reset_token


@pytest.fixture
async def expired_reset_token(async_session: AsyncSession, test_user: User):
    """Create an expired password reset token."""
    expires_at = datetime.now() - timedelta(minutes=30)
    reset_token = PasswordReset(
        user_id=test_user.id,
        token="expired-token",
        expires_at=expires_at,
    )
    async_session.add(reset_token)
    await async_session.commit()
    await async_session.refresh(reset_token)
    return reset_token


@pytest.fixture
async def used_reset_token(async_session: AsyncSession, test_user: User):
    """Create a used password reset token."""
    expires_at = datetime.now() + timedelta(minutes=30)
    reset_token = PasswordReset(
        user_id=test_user.id,
        token="used-token",
        is_used=True,
        expires_at=expires_at,
    )
    async_session.add(reset_token)
    await async_session.commit()
    await async_session.refresh(reset_token)
    return reset_token


@pytest.mark.asyncio
async def test_request_password_reset(client: TestClient, test_user: User):
    """Test requesting a password reset."""
    response = client.post(
        "/api/v1/password-reset/request",
        json={"email": test_user.email},
    )
    assert response.status_code == 202
    assert "message" in response.json()


@pytest.mark.asyncio
async def test_request_password_reset_nonexistent_email(client: TestClient):
    """Test requesting a password reset for a nonexistent email."""
    response = client.post(
        "/api/v1/password-reset/request",
        json={"email": "<EMAIL>"},
    )
    assert response.status_code == 202
    assert "message" in response.json()


@pytest.mark.asyncio
async def test_verify_valid_token(client: TestClient, test_reset_token: PasswordReset):
    """Test verifying a valid password reset token."""
    response = client.post(
        "/api/v1/password-reset/verify",
        json={"token": test_reset_token.token},
    )
    assert response.status_code == 200
    assert "message" in response.json()
    assert "user_id" in response.json()
    assert response.json()["user_id"] == test_reset_token.user_id


@pytest.mark.asyncio
async def test_verify_expired_token(client: TestClient, expired_reset_token: PasswordReset):
    """Test verifying an expired password reset token."""
    response = client.post(
        "/api/v1/password-reset/verify",
        json={"token": expired_reset_token.token},
    )
    assert response.status_code == 400
    assert "detail" in response.json()


@pytest.mark.asyncio
async def test_verify_used_token(client: TestClient, used_reset_token: PasswordReset):
    """Test verifying a used password reset token."""
    response = client.post(
        "/api/v1/password-reset/verify",
        json={"token": used_reset_token.token},
    )
    assert response.status_code == 400
    assert "detail" in response.json()


@pytest.mark.asyncio
async def test_reset_password(
    client: TestClient, test_reset_token: PasswordReset, test_user: User, async_session: AsyncSession
):
    """Test resetting a password with a valid token."""
    new_password = "new_password"
    response = client.post(
        "/api/v1/password-reset/reset",
        json={"token": test_reset_token.token, "new_password": new_password},
    )
    assert response.status_code == 200
    assert "message" in response.json()
    
    # Verify the token is marked as used
    token = await password_reset_crud.get_by_token(async_session, token=test_reset_token.token)
    assert token.is_used
    
    # Verify the password was changed
    user = await async_session.get(User, test_user.id)
    assert verify_password(new_password, user.hashed_password)


@pytest.mark.asyncio
async def test_reset_password_expired_token(
    client: TestClient, expired_reset_token: PasswordReset
):
    """Test resetting a password with an expired token."""
    response = client.post(
        "/api/v1/password-reset/reset",
        json={"token": expired_reset_token.token, "new_password": "new_password"},
    )
    assert response.status_code == 400
    assert "detail" in response.json()


@pytest.mark.asyncio
async def test_reset_password_used_token(
    client: TestClient, used_reset_token: PasswordReset
):
    """Test resetting a password with a used token."""
    response = client.post(
        "/api/v1/password-reset/reset",
        json={"token": used_reset_token.token, "new_password": "new_password"},
    )
    assert response.status_code == 400
    assert "detail" in response.json()
