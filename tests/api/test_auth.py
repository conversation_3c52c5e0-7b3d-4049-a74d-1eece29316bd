import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import get_password_hash
from app.models.user import User, UserRole


@pytest.fixture
async def test_user(async_session: AsyncSession):
    """Create a test user in the database."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Test",
        last_name="User",
        is_active=True,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)
    return user


@pytest.mark.asyncio
async def test_login(client: TestClient, test_user: User):
    """Test login endpoint."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "<EMAIL>", "password": "password"},
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data
    assert data["token_type"] == "bearer"


@pytest.mark.asyncio
async def test_login_incorrect_password(client: TestClient, test_user: User):
    """Test login endpoint with incorrect password."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "<EMAIL>", "password": "wrong_password"},
    )
    assert response.status_code == 401
    data = response.json()
    assert "detail" in data


@pytest.mark.asyncio
async def test_login_nonexistent_user(client: TestClient):
    """Test login endpoint with nonexistent user."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "<EMAIL>", "password": "password"},
    )
    assert response.status_code == 401
    data = response.json()
    assert "detail" in data


@pytest.mark.asyncio
async def test_register(client: TestClient):
    """Test register endpoint."""
    # Mock the rate limiter to allow the request
    from unittest.mock import patch
    with patch('app.utils.rate_limiter.rate_limiter.is_allowed', return_value=True):
        response = client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "password": "password",
                "first_name": "New",
                "last_name": "User",
            },
        )
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Registration successful. Your account is pending admin approval."
    assert data["email"] == "<EMAIL>"
    assert data["is_active"] is False


@pytest.mark.asyncio
async def test_register_existing_email(client: TestClient, test_user: User):
    """Test register endpoint with existing email."""
    # Mock the rate limiter to allow the request
    from unittest.mock import patch
    with patch('app.utils.rate_limiter.rate_limiter.is_allowed', return_value=True):
        response = client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "password": "password",
                "first_name": "Test",
                "last_name": "User",
            },
        )
    assert response.status_code == 400
    data = response.json()
    assert "detail" in data
