import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User, UserRole
from app.models.user_profile import User<PERSON><PERSON><PERSON><PERSON>, KYCStatus
from app.models.wallet import FiatWallet, CryptoWallet
from app.models.wallet_config import WalletConfig
from app.models.transaction import Transaction, TransactionType, TransactionStatus, WalletType
from app.models.support import SupportTicket, TicketStatus, TicketPriority
from app.models.password_reset import PasswordReset
from app.core.security import get_password_hash
from datetime import datetime, timedelta
import uuid


@pytest.mark.asyncio
async def test_create_user(async_session: AsyncSession):
    """Test creating a user."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Test",
        last_name="User",
        is_active=True,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    assert user.id is not None
    assert user.email == "<EMAIL>"
    assert user.first_name == "Test"
    assert user.last_name == "User"
    assert user.is_active is True
    assert user.role == UserRole.USER


@pytest.mark.asyncio
async def test_create_wallets(async_session: AsyncSession):
    """Test creating wallets."""
    # Create user first
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Wallet",
        last_name="Test",
        is_active=True,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    # Create fiat wallet
    fiat_wallet = FiatWallet(
        user_id=user.id,
        balance=1000.0,
        currency="USD",
    )
    async_session.add(fiat_wallet)

    # Create crypto wallet
    crypto_wallet = CryptoWallet(
        user_id=user.id,
        balance=0.5,
        currency="BTC",
        wallet_address="btc-test-address",
    )
    async_session.add(crypto_wallet)

    await async_session.commit()
    await async_session.refresh(fiat_wallet)
    await async_session.refresh(crypto_wallet)

    assert fiat_wallet.id is not None
    assert fiat_wallet.user_id == user.id
    assert fiat_wallet.balance == 1000.0
    assert fiat_wallet.currency == "USD"

    assert crypto_wallet.id is not None
    assert crypto_wallet.user_id == user.id
    assert crypto_wallet.balance == 0.5
    assert crypto_wallet.currency == "BTC"
    assert crypto_wallet.wallet_address == "btc-test-address"


@pytest.mark.asyncio
async def test_create_transaction(async_session: AsyncSession):
    """Test creating a transaction."""
    # Create user first
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Transaction",
        last_name="Test",
        is_active=True,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    # Create transaction
    transaction = Transaction(
        user_id=user.id,
        transaction_type=TransactionType.DEPOSIT,
        amount=100.0,
        fee=0.0,
        source_wallet_type=WalletType.FIAT,
        status=TransactionStatus.COMPLETED,
        reference_id="TEST-123",
        description="Test deposit",
    )
    async_session.add(transaction)
    await async_session.commit()
    await async_session.refresh(transaction)

    assert transaction.id is not None
    assert transaction.user_id == user.id
    assert transaction.transaction_type == TransactionType.DEPOSIT
    assert transaction.amount == 100.0
    assert transaction.source_wallet_type == WalletType.FIAT
    assert transaction.status == TransactionStatus.COMPLETED


@pytest.mark.asyncio
async def test_create_support_ticket(async_session: AsyncSession):
    """Test creating a support ticket."""
    # Create user first
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Support",
        last_name="Test",
        is_active=True,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    # Create support ticket
    ticket = SupportTicket(
        user_id=user.id,
        subject="Test ticket",
        description="This is a test ticket",
        status=TicketStatus.OPEN,
        priority=TicketPriority.MEDIUM,
    )
    async_session.add(ticket)
    await async_session.commit()
    await async_session.refresh(ticket)

    assert ticket.id is not None
    assert ticket.user_id == user.id
    assert ticket.subject == "Test ticket"
    assert ticket.description == "This is a test ticket"
    assert ticket.status == TicketStatus.OPEN
    assert ticket.priority == TicketPriority.MEDIUM


@pytest.mark.asyncio
async def test_create_password_reset(async_session: AsyncSession):
    """Test creating a password reset token."""
    # Create user first
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Reset",
        last_name="Test",
        is_active=True,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    # Create password reset token
    expires_at = datetime.utcnow() + timedelta(minutes=30)
    reset_token = PasswordReset(
        user_id=user.id,
        token="test-token",
        expires_at=expires_at,
    )
    async_session.add(reset_token)
    await async_session.commit()
    await async_session.refresh(reset_token)

    assert reset_token.id is not None
    assert reset_token.user_id == user.id
    assert reset_token.token == "test-token"
    assert reset_token.is_used is False
    assert reset_token.is_valid is True


@pytest.mark.asyncio
async def test_create_user_profile(async_session: AsyncSession):
    """Test creating a user profile with KYC data."""
    # Create user first
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Profile",
        last_name="Test",
        is_active=True,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    # Create user profile
    profile = UserProfile(
        user_id=user.id,
        date_of_birth=datetime(1990, 1, 1),
        phone_number="+1234567890",
        address_line_1="123 Test St",
        city="Test City",
        state_province="Test State",
        postal_code="12345",
        country="Test Country",
        id_document_type="passport",
        id_document_number="P123456789",
        kyc_status=KYCStatus.PENDING,
    )
    async_session.add(profile)
    await async_session.commit()
    await async_session.refresh(profile)

    assert profile.id is not None
    assert profile.user_id == user.id
    assert profile.phone_number == "+1234567890"
    assert profile.address_line_1 == "123 Test St"
    assert profile.kyc_status == KYCStatus.PENDING
    assert profile.avatar_image is None


@pytest.mark.asyncio
async def test_create_wallet_config(async_session: AsyncSession):
    """Test creating a wallet configuration."""
    # Create admin user first
    admin_user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Admin",
        last_name="User",
        is_active=True,
        role=UserRole.ADMIN,
    )
    async_session.add(admin_user)
    await async_session.commit()
    await async_session.refresh(admin_user)

    # Create wallet config
    config = WalletConfig(
        config_type="default_crypto_address",
        currency="BTC",
        default_address="btc-default-address-123",
        description="Default BTC address for new users",
        is_active=True,
        created_by=admin_user.id,
        updated_by=admin_user.id,
    )
    async_session.add(config)
    await async_session.commit()
    await async_session.refresh(config)

    assert config.id is not None
    assert config.currency == "BTC"
    assert config.default_address == "btc-default-address-123"
    assert config.is_active is True
    assert config.created_by == admin_user.id


@pytest.mark.asyncio
async def test_user_uuid_generation(async_session: AsyncSession):
    """Test that UUIDs are properly generated for users."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="UUID",
        last_name="Test",
        is_active=True,
        role=UserRole.USER,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    # Check that ID is a valid UUID string
    assert user.id is not None
    assert isinstance(user.id, str)
    assert len(user.id) == 36  # UUID string length
    assert user.id.count('-') == 4  # UUID format

    # Test that we can parse it as UUID
    parsed_uuid = uuid.UUID(user.id)
    assert str(parsed_uuid) == user.id


@pytest.mark.asyncio
async def test_user_withdraw_limits(async_session: AsyncSession):
    """Test user withdraw limits functionality."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        first_name="Limits",
        last_name="Test",
        is_active=True,
        role=UserRole.USER,
        fiat_withdraw_limit=5000.0,
        crypto_withdraw_limit=0.5,
    )
    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    assert user.fiat_withdraw_limit == 5000.0
    assert user.crypto_withdraw_limit == 0.5
