from datetime import datetime
from typing import Optional
import uuid

from sqlmodel import Field, SQLModel


class WalletConfig(SQLModel, table=True):
    """Configuration for default crypto wallet addresses that admin can set."""
    __tablename__ = "wallet_configs"

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)

    # Configuration type (only for crypto)
    config_type: str = Field(index=True)  # 'default_crypto_address'

    # Wallet configuration
    currency: str = Field(index=True)  # BTC, ETH, etc. (crypto currencies only)
    default_address: str  # Default crypto address for new users

    # Metadata
    description: Optional[str] = None
    is_active: bool = Field(default=True)

    # Admin who set this configuration
    created_by: str = Field(foreign_key="users.id")
    updated_by: str = Field(foreign_key="users.id")

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
