from datetime import datetime
from enum import Enum
from typing import Optional, TYPE_CHECKING
import uuid

from sqlmodel import Field, Relationship, SQLModel

if TYPE_CHECKING:
    from app.models.user import User


class TransactionType(str, Enum):
    DEPOSIT = "deposit"
    WITHDRAWAL = "withdrawal"
    CONVERSION = "conversion"


class TransactionStatus(str, Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class WithdrawalMethod(str, Enum):
    WIRE_TRANSFER = "wire_transfer"
    CARD = "card"
    CRYPTO = "crypto"


class WalletType(str, Enum):
    FIAT = "fiat"
    CRYPTO = "crypto"


class Transaction(SQLModel, table=True):
    __tablename__ = "transactions"

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    user_id: str = Field(foreign_key="users.id")
    transaction_type: TransactionType
    amount: float
    fee: float = Field(default=0.0)
    source_wallet_type: WalletType
    destination_wallet_type: Optional[WalletType] = None  # For conversions
    status: TransactionStatus = Field(default=TransactionStatus.PENDING)
    withdrawal_method: Optional[WithdrawalMethod] = None  # For withdrawals
    reference_id: Optional[str] = Field(default=None, index=True)
    description: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None

    # For conversions
    conversion_rate: Optional[float] = None

    # Relationships
    user: "User" = Relationship(back_populates="transactions")
