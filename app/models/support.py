from datetime import datetime
from enum import Enum
from typing import List, Optional, TYPE_CHECKING
import uuid

from sqlmodel import Field, Relationship, SQLModel

if TYPE_CHECKING:
    from app.models.user import User


class TicketStatus(str, Enum):
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    RESOLVED = "resolved"
    CLOSED = "closed"


class TicketPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class SupportTicket(SQLModel, table=True):
    __tablename__ = "support_tickets"

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    user_id: str = Field(foreign_key="users.id")
    subject: str
    description: str
    status: TicketStatus = Field(default=TicketStatus.OPEN)
    priority: TicketPriority = Field(default=TicketPriority.MEDIUM)
    account_manager_id: Optional[str] = Field(
        default=None, foreign_key="users.id"
    )
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    resolved_at: Optional[datetime] = None

    # Relationships
    user: "User" = Relationship(
        back_populates="support_tickets",
        sa_relationship_kwargs={"foreign_keys": "[SupportTicket.user_id]"}
    )
    account_manager: Optional["User"] = Relationship(
        back_populates="assigned_tickets",
        sa_relationship_kwargs={"foreign_keys": "[SupportTicket.account_manager_id]"}
    )
    messages: List["TicketMessage"] = Relationship(back_populates="ticket")


class TicketMessage(SQLModel, table=True):
    __tablename__ = "ticket_messages"

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    ticket_id: str = Field(foreign_key="support_tickets.id")
    sender_id: str = Field(foreign_key="users.id")
    message: str
    created_at: datetime = Field(default_factory=datetime.now)

    # Relationships
    ticket: SupportTicket = Relationship(back_populates="messages")
    sender: "User" = Relationship()
