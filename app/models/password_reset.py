from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional, TYPE_CHECKING
import uuid

from sqlmodel import Field, Relationship, SQLModel

if TYPE_CHECKING:
    from app.models.user import User


class PasswordReset(SQLModel, table=True):
    __tablename__ = "password_resets"

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    user_id: str = Field(foreign_key="users.id")
    token: str = Field(index=True)
    is_used: bool = Field(default=False)
    expires_at: datetime
    created_at: datetime = Field(default_factory=datetime.now)

    # Relationships
    user: "User" = Relationship()

    @property
    def is_expired(self) -> bool:
        """Check if the token is expired."""
        return datetime.now() > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Check if the token is valid (not used and not expired)."""
        return not self.is_used and not self.is_expired
