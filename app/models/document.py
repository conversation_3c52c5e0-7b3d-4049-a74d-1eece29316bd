"""
Document model for KYC document uploads and verification.
"""
from datetime import datetime
from enum import Enum
from typing import Optional

from sqlmodel import SQLModel, Field, Relationship

from app.models.base import BaseModel


class DocumentType(str, Enum):
    """Document types for KYC verification."""
    ID_DOCUMENT = "id_document"  # passport, driver's license, national ID
    PROOF_OF_ADDRESS = "proof_of_address"  # utility bill, bank statement
    SELFIE_WITH_ID = "selfie_with_id"  # selfie holding ID document


class DocumentStatus(str, Enum):
    """Document verification status."""
    PENDING = "pending"  # Uploaded, awaiting review
    UNDER_REVIEW = "under_review"  # Being reviewed by admin
    APPROVED = "approved"  # Verified and approved
    REJECTED = "rejected"  # Rejected with reason


class Document(BaseModel, table=True):
    """Document model for storing uploaded KYC documents."""
    
    __tablename__ = "documents"
    
    # Foreign key to user
    user_id: str = Field(foreign_key="users.id", index=True)
    
    # Document information
    document_type: DocumentType = Field(description="Type of document")
    filename: str = Field(description="Original filename")
    content_type: str = Field(description="MIME type of the file")
    file_size: int = Field(description="File size in bytes")
    
    # Document content (stored as binary data)
    file_data: bytes = Field(description="Binary file data")
    
    # Verification information
    status: DocumentStatus = Field(default=DocumentStatus.PENDING, description="Verification status")
    reviewed_by: Optional[str] = Field(default=None, foreign_key="users.id", description="Admin who reviewed the document")
    reviewed_at: Optional[datetime] = Field(default=None, description="When the document was reviewed")
    rejection_reason: Optional[str] = Field(default=None, description="Reason for rejection if status is REJECTED")
    
    # Additional metadata
    upload_ip: Optional[str] = Field(default=None, description="IP address from which document was uploaded")
    
    # Relationships
    user: Optional["User"] = Relationship(back_populates="documents", foreign_keys=[user_id])
    reviewer: Optional["User"] = Relationship(foreign_keys=[reviewed_by])


# Add the relationship to User model (this will be imported in user.py)
# documents: List["Document"] = Relationship(back_populates="user", foreign_keys="Document.user_id")
