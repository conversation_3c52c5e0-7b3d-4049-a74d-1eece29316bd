from datetime import datetime
from typing import Optional, TYPE_CHECKING
import uuid

from sqlmodel import Field, Relationship, SQLModel

if TYPE_CHECKING:
    from app.models.user import User


class FiatWallet(SQLModel, table=True):
    __tablename__ = "fiat_wallets"

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    user_id: str = Field(foreign_key="users.id", unique=True)
    balance: float = Field(default=0.0)
    currency: str = Field(default="USD")

    # Note: Fiat wallets don't have wallet addresses - only crypto wallets do

    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    # Relationships
    user: "User" = Relationship(back_populates="fiat_wallet")


class CryptoWallet(SQLModel, table=True):
    __tablename__ = "crypto_wallets"

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    user_id: str = Field(foreign_key="users.id", unique=True)
    balance: float = Field(default=0.0)
    currency: str = Field(default="BTC")

    # Admin-managed wallet address (can be changed by admin)
    wallet_address: str = Field(index=True)  # Removed unique constraint for admin flexibility

    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    # Relationships
    user: "User" = Relationship(back_populates="crypto_wallet")
