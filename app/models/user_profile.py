from datetime import datetime
from enum import Enum
from typing import Optional, TYPE_CHECKING
import uuid

from sqlmodel import Field, Relationship, SQLModel, Column, LargeBinary

if TYPE_CHECKING:
    from app.models.user import User


class KYCStatus(str, Enum):
    PENDING = "pending"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    REJECTED = "rejected"


class UserProfile(SQLModel, table=True):
    __tablename__ = "user_profiles"

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    user_id: str = Field(foreign_key="users.id", unique=True)

    # KYC Information
    date_of_birth: Optional[datetime] = None
    phone_number: Optional[str] = None
    address_line_1: Optional[str] = None
    address_line_2: Optional[str] = None
    city: Optional[str] = None
    state_province: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

    # Identity Documents
    id_document_type: Optional[str] = None  # passport, driver_license, national_id
    id_document_number: Optional[str] = None
    id_document_expiry: Optional[datetime] = None

    # Avatar Image (max 5MB)
    avatar_image: Optional[bytes] = Field(default=None, sa_column=Column(LargeBinary))
    avatar_image_filename: Optional[str] = None
    avatar_image_content_type: Optional[str] = None
    avatar_image_size: Optional[int] = None

    # KYC Status
    kyc_status: KYCStatus = Field(default=KYCStatus.PENDING)
    kyc_submitted_at: Optional[datetime] = None
    kyc_reviewed_at: Optional[datetime] = None
    kyc_reviewed_by: Optional[str] = Field(default=None, foreign_key="users.id")
    kyc_rejection_reason: Optional[str] = None

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    # Relationships
    user: "User" = Relationship(
        back_populates="profile",
        sa_relationship_kwargs={"foreign_keys": "[UserProfile.user_id]"}
    )
    reviewed_by_user: Optional["User"] = Relationship(
        sa_relationship_kwargs={"foreign_keys": "[UserProfile.kyc_reviewed_by]"}
    )
