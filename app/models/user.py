from datetime import datetime
from enum import Enum
from typing import List, Optional, TYPE_CHECKING
import uuid

from sqlmodel import Field, Relationship, SQLModel

if TYPE_CHECKING:
    from app.models.wallet import FiatWallet, CryptoWallet
    from app.models.transaction import Transaction
    from app.models.support import SupportTicket
    from app.models.user_profile import UserProfile
    from app.models.document import Document


class UserRole(str, Enum):
    ADMIN = "admin"
    USER = "user"
    ACCOUNT_MANAGER = "account_manager"


class UserBase(SQLModel):
    email: str = Field(unique=True, index=True)
    first_name: str
    last_name: str
    is_active: bool = Field(default=True)
    role: UserRole = Field(default=UserRole.USER)


class User(UserBase, table=True):
    __tablename__ = "users"

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    hashed_password: str
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    # Admin-controlled withdraw limits
    fiat_withdraw_limit: float = Field(default=10000.0)  # Default $10,000 limit
    crypto_withdraw_limit: float = Field(default=1.0)    # Default 1 BTC limit

    # Withdraw limit tracking (admin-controlled reset)
    fiat_withdraw_used: float = Field(default=0.0)  # Amount used from current limit
    crypto_withdraw_used: float = Field(default=0.0)  # Amount used from current limit

    # Relationships
    fiat_wallet: Optional["FiatWallet"] = Relationship(back_populates="user")
    crypto_wallet: Optional["CryptoWallet"] = Relationship(back_populates="user")
    transactions: List["Transaction"] = Relationship(back_populates="user")
    support_tickets: List["SupportTicket"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"foreign_keys": "[SupportTicket.user_id]"}
    )
    profile: Optional["UserProfile"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"foreign_keys": "[UserProfile.user_id]"}
    )

    # For account managers
    assigned_tickets: List["SupportTicket"] = Relationship(
        back_populates="account_manager",
        sa_relationship_kwargs={"foreign_keys": "[SupportTicket.account_manager_id]"}
    )

    # Documents for KYC verification
    documents: List["Document"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"foreign_keys": "[Document.user_id]"}
    )
