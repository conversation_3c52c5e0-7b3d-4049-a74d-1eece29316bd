from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import (
    get_current_active_user,
    get_current_admin_user,
)
from app.crud import user as user_crud
from app.crud import user_profile as user_profile_crud
from app.crud import fiat_wallet as fiat_wallet_crud
from app.crud import crypto_wallet as crypto_wallet_crud
from app.crud import wallet_config as wallet_config_crud
from app.db.session import get_db
from app.models.user import User, UserRole
from app.schemas.user import User as UserSchema
from app.schemas.user import UserCreate, UserUpdate, UserWithWallets, AdminPasswordReset
from app.schemas.user_profile import WithdrawLimitUpdate, UserProfileCreate
from app.schemas.pagination import PaginationParams, PaginatedResponse
from app.utils.pagination import paginate_users

router = APIRouter()


@router.get("/me", response_model=UserWithWallets)
async def read_user_me(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get current user with wallet balances.
    """
    fiat_wallet = await fiat_wallet_crud.get_by_user_id(db, user_id=current_user.id)
    crypto_wallet = await crypto_wallet_crud.get_by_user_id(db, user_id=current_user.id)

    return {
        **current_user.__dict__,
        "fiat_wallet_balance": fiat_wallet.balance if fiat_wallet else 0.0,
        "crypto_wallet_balance": crypto_wallet.balance if crypto_wallet else 0.0,
    }


@router.put("/me", response_model=UserSchema)
async def update_user_me(
    user_in: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update current user.
    """
    user = await user_crud.update(db, db_obj=current_user, obj_in=user_in)
    return user


@router.get("", response_model=PaginatedResponse[UserSchema])
async def read_users(
    request: Request,
    page: int = 1,
    page_size: int = 20,
    role: Optional[str] = None,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Retrieve users with pagination. Admin only.

    - **page**: Page number (default: 1)
    - **page_size**: Items per page (default: 20, max: 100)
    - **role**: Filter by role (default: USER only, excludes ADMIN and ACCOUNT_MANAGER)
    """
    # Default to USER role only (exclude ADMIN and ACCOUNT_MANAGER)
    if role is None:
        role = UserRole.USER.value

    # Validate page_size
    if page_size > 100:
        page_size = 100

    # Build base URL for pagination
    base_url = str(request.url).split('?')[0]

    # Build query parameters for pagination links
    query_params = {"page_size": page_size}
    if role:
        query_params["role"] = role

    return await paginate_users(
        db=db,
        page=page,
        page_size=page_size,
        role_filter=role,
        base_url=base_url,
        query_params=query_params
    )


@router.post("", response_model=UserSchema)
async def create_user(
    user_in: UserCreate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Create new user with automatic profile and wallet creation. Admin only.
    """
    user = await user_crud.get_by_email(db, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A user with this email already exists",
        )

    # Ensure admin-created users are active by default
    user_data = user_in.dict()
    user_data["is_active"] = True  # Admin-created users are active by default
    user = await user_crud.create(db, obj_in=UserCreate(**user_data))

    # Create user profile with default values
    profile_data = UserProfileCreate(user_id=user.id)
    await user_profile_crud.create(db, obj_in=profile_data)

    # Create fiat wallet (USD)
    await fiat_wallet_crud.create(db, obj_in={
        "user_id": user.id,
        "balance": 0.0,
        "currency": "USD"
    })

    # Create crypto wallet (BTC) with default address
    default_config = await wallet_config_crud.get_by_currency(db, currency="BTC")
    default_address = default_config.default_address if default_config else f"btc-{user.id[:16]}"

    await crypto_wallet_crud.create(db, obj_in={
        "user_id": user.id,
        "balance": 0.0,
        "currency": "BTC",
        "wallet_address": default_address
    })

    return user


@router.get("/{user_id}", response_model=UserSchema)
async def read_user_by_id(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get a specific user by id. Admin only.
    """
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )
    return user


@router.put("/{user_id}", response_model=UserSchema)
async def update_user(
    user_id: str,
    user_in: UserUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update a user. Admin only.
    """
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )
    user = await user_crud.update(db, db_obj=user, obj_in=user_in)
    return user


@router.put("/{user_id}/withdraw-limits", response_model=UserSchema, tags=["admin"])
async def update_user_withdraw_limits(
    user_id: str,
    limits_in: WithdrawLimitUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update user withdraw limits. Admin only.
    """
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    user = await user_crud.update_withdraw_limits(
        db,
        db_obj=user,
        fiat_limit=limits_in.fiat_withdraw_limit,
        crypto_limit=limits_in.crypto_withdraw_limit
    )
    return user


@router.get("/me/withdraw-limits")
async def get_my_withdraw_limits(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get current user's available withdraw limits.
    """
    limits = await user_crud.get_available_withdraw_limits(db, user_id=current_user.id)
    return limits


@router.put("/{user_id}/reset-withdraw-usage", response_model=UserSchema, tags=["admin"])
async def reset_user_withdraw_usage(
    user_id: str,
    reset_fiat: bool = False,
    reset_crypto: bool = False,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Reset user withdraw usage. Admin only.
    """
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    user = await user_crud.reset_withdraw_usage(
        db,
        db_obj=user,
        reset_fiat=reset_fiat,
        reset_crypto=reset_crypto
    )
    return user


@router.put("/{user_id}/reset-password", response_model=UserSchema, tags=["admin"])
async def reset_user_password(
    user_id: str,
    password_reset: AdminPasswordReset,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Reset user password. Admin only.
    If no password provided, defaults to 'default123'.
    """
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    # Use provided password or default
    new_password = password_reset.new_password or "default123"

    # Update user password
    user = await user_crud.update(
        db,
        db_obj=user,
        obj_in={"password": new_password}
    )
    return user
