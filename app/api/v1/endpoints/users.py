from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import (
    get_current_active_user,
    get_current_admin_user,
)
from app.crud import user as user_crud
from app.crud import fiat_wallet as fiat_wallet_crud
from app.crud import crypto_wallet as crypto_wallet_crud
from app.db.session import get_db
from app.models.user import User
from app.schemas.user import User as UserSchema
from app.schemas.user import UserCreate, UserUpdate, UserWithWallets
from app.schemas.user_profile import WithdrawLimitUpdate

router = APIRouter()


@router.get("/me", response_model=UserWithWallets)
async def read_user_me(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get current user with wallet balances.
    """
    fiat_wallet = await fiat_wallet_crud.get_by_user_id(db, user_id=current_user.id)
    crypto_wallet = await crypto_wallet_crud.get_by_user_id(db, user_id=current_user.id)

    return {
        **current_user.__dict__,
        "fiat_wallet_balance": fiat_wallet.balance if fiat_wallet else 0.0,
        "crypto_wallet_balance": crypto_wallet.balance if crypto_wallet else 0.0,
    }


@router.put("/me", response_model=UserSchema)
async def update_user_me(
    user_in: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update current user.
    """
    user = await user_crud.update(db, db_obj=current_user, obj_in=user_in)
    return user


@router.get("", response_model=List[UserSchema])
async def read_users(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Retrieve users. Admin only.
    """
    users = await user_crud.get_multi(db, skip=skip, limit=limit)
    return users


@router.post("", response_model=UserSchema)
async def create_user(
    user_in: UserCreate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Create new user. Admin only.
    """
    user = await user_crud.get_by_email(db, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A user with this email already exists",
        )
    user = await user_crud.create(db, obj_in=user_in)
    return user


@router.get("/{user_id}", response_model=UserSchema)
async def read_user_by_id(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get a specific user by id. Admin only.
    """
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )
    return user


@router.put("/{user_id}", response_model=UserSchema)
async def update_user(
    user_id: str,
    user_in: UserUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update a user. Admin only.
    """
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )
    user = await user_crud.update(db, db_obj=user, obj_in=user_in)
    return user


@router.put("/{user_id}/withdraw-limits", response_model=UserSchema, tags=["admin"])
async def update_user_withdraw_limits(
    user_id: str,
    limits_in: WithdrawLimitUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update user withdraw limits. Admin only.
    """
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    user = await user_crud.update_withdraw_limits(
        db,
        db_obj=user,
        fiat_limit=limits_in.fiat_withdraw_limit,
        crypto_limit=limits_in.crypto_withdraw_limit
    )
    return user
