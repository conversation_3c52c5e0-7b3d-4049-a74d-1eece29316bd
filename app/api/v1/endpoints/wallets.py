from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import (
    get_current_active_user,
    get_current_admin_user,
)
from app.crud import fiat_wallet as fiat_wallet_crud
from app.crud import crypto_wallet as crypto_wallet_crud
from app.crud import wallet_config as wallet_config_crud
from app.db.session import get_db
from app.models.user import User
from app.schemas.wallet import FiatWallet, CryptoWallet, WalletUpdate
import uuid

router = APIRouter()


@router.get("/fiat/me", response_model=FiatWallet)
async def read_fiat_wallet_me(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get current user's fiat wallet.
    """
    wallet = await fiat_wallet_crud.get_by_user_id(db, user_id=current_user.id)
    if not wallet:
        # Create wallet if it doesn't exist
        wallet = await fiat_wallet_crud.create(
            db, obj_in={"user_id": current_user.id, "balance": 0.0, "currency": "USD"}
        )
    return wallet


@router.get("/crypto/me", response_model=CryptoWallet)
async def read_crypto_wallet_me(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get current user's crypto wallet.
    """
    wallet = await crypto_wallet_crud.get_by_user_id(db, user_id=current_user.id)
    if not wallet:
        # Get default address from config or generate one
        default_config = await wallet_config_crud.get_by_currency(db, currency="BTC")
        default_address = default_config.default_address if default_config else f"btc-{uuid.uuid4().hex[:16]}"

        # Create wallet if it doesn't exist
        wallet = await crypto_wallet_crud.create(
            db,
            obj_in={
                "user_id": current_user.id,
                "balance": 0.0,
                "currency": "BTC",
                "wallet_address": default_address,
            },
        )
    return wallet


@router.get("/fiat/{user_id}", response_model=FiatWallet, tags=["admin"])
async def read_fiat_wallet(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get a user's fiat wallet. Admin only.
    """
    wallet = await fiat_wallet_crud.get_by_user_id(db, user_id=user_id)
    if not wallet:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Wallet not found"
        )
    return wallet


@router.get("/crypto/{user_id}", response_model=CryptoWallet, tags=["admin"])
async def read_crypto_wallet(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get a user's crypto wallet. Admin only.
    """
    wallet = await crypto_wallet_crud.get_by_user_id(db, user_id=user_id)
    if not wallet:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Wallet not found"
        )
    return wallet


@router.put("/fiat/{wallet_id}", response_model=FiatWallet, tags=["admin"])
async def update_fiat_wallet(
    wallet_id: str,
    wallet_in: WalletUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update a fiat wallet. Admin only.
    """
    wallet = await fiat_wallet_crud.get(db, id=wallet_id)
    if not wallet:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Wallet not found"
        )
    wallet = await fiat_wallet_crud.update(db, db_obj=wallet, obj_in=wallet_in)
    return wallet


@router.put("/crypto/{wallet_id}", response_model=CryptoWallet, tags=["admin"])
async def update_crypto_wallet(
    wallet_id: str,
    wallet_in: WalletUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update a crypto wallet. Admin only.
    """
    wallet = await crypto_wallet_crud.get(db, id=wallet_id)
    if not wallet:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Wallet not found"
        )
    wallet = await crypto_wallet_crud.update(db, db_obj=wallet, obj_in=wallet_in)
    return wallet
