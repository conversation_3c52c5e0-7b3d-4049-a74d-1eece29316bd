from datetime import datetime
from typing import Any, List
import uuid

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import (
    get_current_active_user,
    get_current_admin_user,
)
from app.crud import transaction as transaction_crud
from app.crud import fiat_wallet as fiat_wallet_crud
from app.crud import crypto_wallet as crypto_wallet_crud
from app.db.session import get_db
from app.models.transaction import (
    TransactionStatus,
    TransactionType,
    WalletType,
)
from app.models.user import User
from app.schemas.transaction import (
    Transaction,
    TransactionCreate,
    TransactionUpdate,
    DepositCreate,
    WithdrawalCreate,
    ConversionCreate,
)

router = APIRouter()


@router.get("/me", response_model=List[Transaction])
async def read_transactions_me(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get current user's transactions.
    """
    transactions = await transaction_crud.get_by_user_id(
        db, user_id=current_user.id, skip=skip, limit=limit
    )
    return transactions


@router.post("/deposit", response_model=Transaction)
async def create_deposit(
    deposit_in: DepositCreate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Create a deposit transaction. Admin only.
    """
    # Create transaction
    transaction_data = {
        "user_id": current_user.id,
        "transaction_type": TransactionType.DEPOSIT,
        "amount": deposit_in.amount,
        "source_wallet_type": deposit_in.wallet_type,
        "description": deposit_in.description,
        "reference_id": f"DEP-{uuid.uuid4().hex[:8]}",
    }
    transaction = await transaction_crud.create(db, obj_in=transaction_data)

    # Update wallet balance
    if deposit_in.wallet_type == WalletType.FIAT:
        wallet = await fiat_wallet_crud.get_by_user_id(db, user_id=current_user.id)
        await fiat_wallet_crud.update_balance(
            db, wallet_id=wallet.id, amount=deposit_in.amount, is_deposit=True
        )
    else:
        wallet = await crypto_wallet_crud.get_by_user_id(db, user_id=current_user.id)
        await crypto_wallet_crud.update_balance(
            db, wallet_id=wallet.id, amount=deposit_in.amount, is_deposit=True
        )

    # Update transaction status
    await transaction_crud.update_status(
        db, transaction_id=transaction.id, status=TransactionStatus.COMPLETED
    )

    return transaction


@router.post("/withdraw", response_model=Transaction)
async def create_withdrawal(
    withdrawal_in: WithdrawalCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Create a withdrawal transaction.
    """
    # Check withdraw limits
    if withdrawal_in.wallet_type == WalletType.FIAT:
        if withdrawal_in.amount > current_user.fiat_withdraw_limit:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Withdrawal amount exceeds daily limit of {current_user.fiat_withdraw_limit}",
            )
        wallet = await fiat_wallet_crud.get_by_user_id(db, user_id=current_user.id)
        if not wallet or wallet.balance < withdrawal_in.amount:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Insufficient fiat balance",
            )
    else:
        if withdrawal_in.amount > current_user.crypto_withdraw_limit:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Withdrawal amount exceeds daily limit of {current_user.crypto_withdraw_limit}",
            )
        wallet = await crypto_wallet_crud.get_by_user_id(db, user_id=current_user.id)
        if not wallet or wallet.balance < withdrawal_in.amount:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Insufficient crypto balance",
            )

    # Create transaction
    transaction_data = {
        "user_id": current_user.id,
        "transaction_type": TransactionType.WITHDRAWAL,
        "amount": withdrawal_in.amount,
        "source_wallet_type": withdrawal_in.wallet_type,
        "withdrawal_method": withdrawal_in.withdrawal_method,
        "description": withdrawal_in.description,
        "reference_id": f"WDR-{uuid.uuid4().hex[:8]}",
    }
    transaction = await transaction_crud.create(db, obj_in=transaction_data)

    # Update wallet balance
    if withdrawal_in.wallet_type == WalletType.FIAT:
        await fiat_wallet_crud.update_balance(
            db, wallet_id=wallet.id, amount=withdrawal_in.amount, is_deposit=False
        )
    else:
        await crypto_wallet_crud.update_balance(
            db, wallet_id=wallet.id, amount=withdrawal_in.amount, is_deposit=False
        )

    # Update transaction status
    await transaction_crud.update_status(
        db, transaction_id=transaction.id, status=TransactionStatus.COMPLETED
    )

    return transaction


@router.post("/convert", response_model=Transaction)
async def create_conversion(
    conversion_in: ConversionCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Create a conversion transaction.
    """
    # Check if source and destination are different
    if conversion_in.source_wallet_type == conversion_in.destination_wallet_type:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Source and destination wallets must be different",
        )

    # Check if user has enough balance in source wallet
    if conversion_in.source_wallet_type == WalletType.FIAT:
        source_wallet = await fiat_wallet_crud.get_by_user_id(db, user_id=current_user.id)
        destination_wallet = await crypto_wallet_crud.get_by_user_id(db, user_id=current_user.id)
        if not source_wallet or source_wallet.balance < conversion_in.amount:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Insufficient fiat balance",
            )
        # Mock conversion rate (in a real app, this would come from an external API)
        conversion_rate = 0.00005  # 1 USD = 0.00005 BTC
        converted_amount = conversion_in.amount * conversion_rate
    else:
        source_wallet = await crypto_wallet_crud.get_by_user_id(db, user_id=current_user.id)
        destination_wallet = await fiat_wallet_crud.get_by_user_id(db, user_id=current_user.id)
        if not source_wallet or source_wallet.balance < conversion_in.amount:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Insufficient crypto balance",
            )
        # Mock conversion rate (in a real app, this would come from an external API)
        conversion_rate = 20000  # 1 BTC = 20000 USD
        converted_amount = conversion_in.amount * conversion_rate

    # Create transaction
    transaction_data = {
        "user_id": current_user.id,
        "transaction_type": TransactionType.CONVERSION,
        "amount": conversion_in.amount,
        "source_wallet_type": conversion_in.source_wallet_type,
        "destination_wallet_type": conversion_in.destination_wallet_type,
        "description": conversion_in.description,
        "reference_id": f"CNV-{uuid.uuid4().hex[:8]}",
        "conversion_rate": conversion_rate,
    }
    transaction = await transaction_crud.create(db, obj_in=transaction_data)

    # Update wallet balances
    if conversion_in.source_wallet_type == WalletType.FIAT:
        await fiat_wallet_crud.update_balance(
            db, wallet_id=source_wallet.id, amount=conversion_in.amount, is_deposit=False
        )
        await crypto_wallet_crud.update_balance(
            db, wallet_id=destination_wallet.id, amount=converted_amount, is_deposit=True
        )
    else:
        await crypto_wallet_crud.update_balance(
            db, wallet_id=source_wallet.id, amount=conversion_in.amount, is_deposit=False
        )
        await fiat_wallet_crud.update_balance(
            db, wallet_id=destination_wallet.id, amount=converted_amount, is_deposit=True
        )

    # Update transaction status
    await transaction_crud.update_status(
        db, transaction_id=transaction.id, status=TransactionStatus.COMPLETED
    )

    return transaction


@router.get("", response_model=List[Transaction])
async def read_transactions(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get all transactions. Admin only.
    """
    transactions = await transaction_crud.get_multi(db, skip=skip, limit=limit)
    return transactions


@router.get("/{transaction_id}", response_model=Transaction)
async def read_transaction(
    transaction_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get a transaction by ID.
    """
    transaction = await transaction_crud.get(db, id=transaction_id)
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Transaction not found"
        )
    if transaction.user_id != current_user.id and current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
        )
    return transaction


@router.put("/{transaction_id}", response_model=Transaction, tags=["admin"])
async def update_transaction(
    transaction_id: str,
    transaction_in: TransactionUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update a transaction. Admin only.
    """
    transaction = await transaction_crud.get(db, id=transaction_id)
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Transaction not found"
        )
    transaction = await transaction_crud.update(
        db, db_obj=transaction, obj_in=transaction_in
    )
    return transaction
