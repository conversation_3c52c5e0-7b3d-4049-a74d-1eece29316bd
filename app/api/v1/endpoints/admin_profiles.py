"""
Admin profile management endpoints.
"""
from typing import Any, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_admin_user
from app.crud import user_profile as user_profile_crud
from app.crud import user as user_crud
from app.db.session import get_db
from app.models.user import User
from app.models.user_profile import KYCStatus
from app.schemas.user_profile import (
    UserProfile as UserProfileSchema,
    UserProfileUpdate,
    KYCStatusUpdate,
)
from app.schemas.pagination import PaginatedResponse
from app.utils.pagination import paginate_user_profiles

router = APIRouter()


@router.get("", response_model=PaginatedResponse[UserProfileSchema], tags=["admin"])
async def get_all_profiles(
    request: Request,
    page: int = 1,
    page_size: int = 20,
    kyc_status: Optional[str] = None,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get all user profiles with pagination. Admin only.
    
    - **page**: Page number (default: 1)
    - **page_size**: Items per page (default: 20, max: 100)
    - **kyc_status**: Filter by KYC status (PENDING, UNDER_REVIEW, APPROVED, REJECTED)
    """
    # Validate page_size
    if page_size > 100:
        page_size = 100
    
    # Build base URL for pagination
    base_url = str(request.url).split('?')[0]
    
    # Build query parameters for pagination links
    query_params = {"page_size": page_size}
    if kyc_status:
        query_params["kyc_status"] = kyc_status
    
    return await paginate_user_profiles(
        db=db,
        page=page,
        page_size=page_size,
        kyc_status=kyc_status,
        base_url=base_url,
        query_params=query_params
    )


@router.get("/{user_id}", response_model=UserProfileSchema, tags=["admin"])
async def get_user_profile(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get a specific user's profile. Admin only.
    """
    profile = await user_profile_crud.get_by_user_id(db, user_id=user_id)
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    return profile


@router.put("/{user_id}", response_model=UserProfileSchema, tags=["admin"])
async def update_user_profile(
    user_id: str,
    profile_update: UserProfileUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update any user's profile. Admin only.
    """
    # Check if user exists
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Get or create profile
    profile = await user_profile_crud.get_by_user_id(db, user_id=user_id)
    if not profile:
        # Create profile if it doesn't exist
        from app.schemas.user_profile import UserProfileCreate
        profile_data = UserProfileCreate(user_id=user_id)
        profile = await user_profile_crud.create(db, obj_in=profile_data)
    
    # Update profile
    profile = await user_profile_crud.update(db, db_obj=profile, obj_in=profile_update)
    return profile


@router.put("/{user_id}/kyc-status", response_model=UserProfileSchema, tags=["admin"])
async def update_user_kyc_status(
    user_id: str,
    kyc_update: KYCStatusUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update user's KYC status. Admin only.
    
    Allowed transitions:
    - PENDING → UNDER_REVIEW
    - UNDER_REVIEW → APPROVED/REJECTED
    - Any status → PENDING (for re-review)
    """
    # Check if user exists
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Get profile
    profile = await user_profile_crud.get_by_user_id(db, user_id=user_id)
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    
    # Validate KYC status transition
    current_status = profile.kyc_status
    new_status = kyc_update.kyc_status
    
    # Define valid transitions
    valid_transitions = {
        KYCStatus.PENDING: [KYCStatus.UNDER_REVIEW, KYCStatus.PENDING],
        KYCStatus.UNDER_REVIEW: [KYCStatus.APPROVED, KYCStatus.REJECTED, KYCStatus.PENDING],
        KYCStatus.APPROVED: [KYCStatus.PENDING],  # Can reset for re-review
        KYCStatus.REJECTED: [KYCStatus.PENDING, KYCStatus.UNDER_REVIEW],  # Can resubmit
    }
    
    if new_status not in valid_transitions.get(current_status, []):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid KYC status transition from {current_status} to {new_status}"
        )
    
    # If rejecting, require rejection reason
    if new_status == KYCStatus.REJECTED and not kyc_update.kyc_rejection_reason:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Rejection reason is required when rejecting KYC"
        )
    
    # Update KYC status
    update_data = {
        "kyc_status": new_status,
        "kyc_rejection_reason": kyc_update.kyc_rejection_reason if new_status == KYCStatus.REJECTED else None
    }
    
    profile = await user_profile_crud.update(db, db_obj=profile, obj_in=update_data)
    return profile


@router.get("/{user_id}/kyc-history", tags=["admin"])
async def get_user_kyc_history(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get user's KYC status history. Admin only.
    """
    # Check if user exists
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Get profile
    profile = await user_profile_crud.get_by_user_id(db, user_id=user_id)
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    
    # Return KYC information
    return {
        "user_id": user_id,
        "current_kyc_status": profile.kyc_status,
        "kyc_rejection_reason": profile.kyc_rejection_reason,
        "profile_created_at": profile.created_at,
        "profile_updated_at": profile.updated_at,
        "user_email": user.email,
        "user_name": f"{user.first_name} {user.last_name}",
    }
