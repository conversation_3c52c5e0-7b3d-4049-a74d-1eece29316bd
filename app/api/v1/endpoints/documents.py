"""
Document upload and verification endpoints.
"""
from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_active_user, get_current_admin_user
from app.crud import document as document_crud
from app.db.session import get_db
from app.models.user import User
from app.models.document import DocumentType, DocumentStatus
from app.schemas.document import (
    DocumentUpload,
    DocumentResponse,
    DocumentWithData,
    DocumentVerification,
    UserDocumentsSummary,
)

router = APIRouter()


def get_client_ip(request: Request) -> str:
    """Get client IP address."""
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip.strip()
    
    return request.client.host if request.client else "unknown"


@router.post("/upload", response_model=DocumentResponse)
async def upload_document(
    request: Request,
    document_type: DocumentType,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Upload a document for KYC verification.
    
    - **document_type**: Type of document (id_document, proof_of_address, selfie_with_id)
    - **file**: Document file (PDF, JPG, PNG, max 10MB)
    """
    # Validate file type
    allowed_types = ["image/jpeg", "image/jpg", "image/png", "application/pdf"]
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid file type. Allowed types: {', '.join(allowed_types)}"
        )
    
    # Read file data
    file_data = await file.read()
    
    # Validate file size (10MB limit)
    max_size = 10 * 1024 * 1024  # 10MB
    if len(file_data) > max_size:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File size exceeds 10MB limit"
        )
    
    # Check if user already has this document type
    existing_doc = await document_crud.check_document_exists(
        db, user_id=current_user.id, document_type=document_type
    )
    if existing_doc:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Document of type {document_type} already exists. Delete the existing document first."
        )
    
    # Create document upload data
    document_data = DocumentUpload(
        document_type=document_type,
        filename=file.filename or "unknown",
        content_type=file.content_type
    )
    
    # Get client IP
    client_ip = get_client_ip(request)
    
    # Create document
    document = await document_crud.create_document(
        db,
        user_id=current_user.id,
        document_data=document_data,
        file_data=file_data,
        upload_ip=client_ip
    )
    
    return document


@router.get("/me", response_model=UserDocumentsSummary)
async def get_my_documents(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get current user's documents summary.
    """
    summary = await document_crud.get_user_document_summary(db, user_id=current_user.id)
    return summary


@router.delete("/{document_id}")
async def delete_my_document(
    document_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Delete a document (only if not approved).
    """
    success = await document_crud.delete_document(
        db, document_id=document_id, user_id=current_user.id
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found or cannot be deleted"
        )
    
    return {"message": "Document deleted successfully"}


# Admin endpoints
@router.get("/admin/users/{user_id}", response_model=UserDocumentsSummary, tags=["admin"])
async def get_user_documents(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get user's documents summary. Admin only.
    """
    summary = await document_crud.get_user_document_summary(db, user_id=user_id)
    return summary


@router.get("/admin/users/{user_id}/{document_id}", response_model=DocumentWithData, tags=["admin"])
async def get_document_with_data(
    user_id: str,
    document_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get document with file data for review. Admin only.
    """
    document = await document_crud.get(db, id=document_id)
    if not document or document.user_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    return document


@router.put("/admin/users/{user_id}/{document_id}/verify", response_model=DocumentResponse, tags=["admin"])
async def verify_document(
    user_id: str,
    document_id: str,
    verification: DocumentVerification,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Verify or reject a document. Admin only.
    """
    # Validate rejection reason
    if verification.status == DocumentStatus.REJECTED and not verification.rejection_reason:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Rejection reason is required when rejecting a document"
        )
    
    # Verify document
    document = await document_crud.verify_document(
        db,
        document_id=document_id,
        status=verification.status,
        reviewed_by=current_user.id,
        rejection_reason=verification.rejection_reason
    )
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    if document.user_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Document does not belong to specified user"
        )
    
    return document


@router.get("/admin/pending", response_model=List[DocumentResponse], tags=["admin"])
async def get_pending_documents(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get all pending documents for review. Admin only.
    """
    documents = await document_crud.get_by_status(
        db, status=DocumentStatus.PENDING, skip=skip, limit=limit
    )
    return documents
