from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_admin_user
from app.crud import wallet_config as wallet_config_crud
from app.crud import crypto_wallet as crypto_wallet_crud
from app.db.session import get_db
from app.models.user import User
from app.schemas.wallet_config import (
    WalletConfig,
    WalletConfigCreate,
    WalletConfigUpdate,
)
from app.schemas.wallet import CryptoWalletAddressUpdate

router = APIRouter()


# Wallet Configuration Management
@router.get("/wallet-configs", response_model=List[WalletConfig])
async def get_wallet_configs(
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get all wallet configurations. Admin only.
    """
    configs = await wallet_config_crud.get_active_configs(db)
    return configs


@router.post("/wallet-configs", response_model=WalletConfig)
async def create_wallet_config(
    config_in: WalletConfigCreate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Create new wallet configuration. Admin only.
    """
    # Check if config already exists for this currency
    existing_config = await wallet_config_crud.get_by_currency(db, currency=config_in.currency)
    if existing_config:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Wallet configuration for {config_in.currency} already exists"
        )
    
    # Set created_by and updated_by to current admin
    config_data = config_in.dict()
    config_data["created_by"] = current_user.id
    config_data["updated_by"] = current_user.id
    
    config = await wallet_config_crud.create(db, obj_in=WalletConfigCreate(**config_data))
    return config


@router.put("/wallet-configs/{config_id}", response_model=WalletConfig)
async def update_wallet_config(
    config_id: str,
    config_in: WalletConfigUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update wallet configuration. Admin only.
    """
    config = await wallet_config_crud.get(db, id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Wallet configuration not found"
        )
    
    # Set updated_by to current admin
    update_data = config_in.dict(exclude_unset=True)
    update_data["updated_by"] = current_user.id
    
    config = await wallet_config_crud.update(db, db_obj=config, obj_in=update_data)
    return config


@router.delete("/wallet-configs/{config_id}")
async def deactivate_wallet_config(
    config_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Deactivate wallet configuration. Admin only.
    """
    config = await wallet_config_crud.get(db, id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Wallet configuration not found"
        )
    
    config = await wallet_config_crud.deactivate_config(db, db_obj=config)
    return {"message": "Wallet configuration deactivated successfully"}


@router.get("/wallet-configs/currency/{currency}", response_model=WalletConfig)
async def get_wallet_config_by_currency(
    currency: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get wallet configuration by currency. Admin only.
    """
    config = await wallet_config_crud.get_by_currency(db, currency=currency)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No wallet configuration found for {currency}"
        )
    return config


# Crypto Wallet Address Management
@router.put("/users/{user_id}/crypto-wallet-address")
async def update_user_crypto_wallet_address(
    user_id: str,
    address_update: CryptoWalletAddressUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update user's crypto wallet address. Admin only.
    """
    crypto_wallet = await crypto_wallet_crud.get_by_user_id(db, user_id=user_id)
    if not crypto_wallet:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Crypto wallet not found for this user"
        )
    
    # Check if address is already in use (optional - removed unique constraint for flexibility)
    existing_wallet = await crypto_wallet_crud.get_by_wallet_address(
        db, wallet_address=address_update.wallet_address
    )
    if existing_wallet and existing_wallet.id != crypto_wallet.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="This wallet address is already in use by another user"
        )
    
    crypto_wallet = await crypto_wallet_crud.update_wallet_address(
        db, db_obj=crypto_wallet, new_address=address_update.wallet_address
    )
    
    return {
        "message": "Crypto wallet address updated successfully",
        "user_id": user_id,
        "new_address": address_update.wallet_address
    }
