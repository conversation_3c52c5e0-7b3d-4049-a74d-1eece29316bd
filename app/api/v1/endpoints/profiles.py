from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import (
    get_current_active_user,
    get_current_admin_user,
)
from app.crud import user_profile as user_profile_crud
from app.db.session import get_db
from app.models.user import User
from app.models.user_profile import KYCStatus
from app.schemas.user_profile import (
    UserProfile,
    UserProfileCreate,
    UserProfileUpdate,
    UserProfileWithAvatar,
    KYCReview,
)

router = APIRouter()


@router.get("/me", response_model=UserProfile)
async def get_my_profile(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get current user's profile.
    """
    profile = await user_profile_crud.get_by_user_id(db, user_id=current_user.id)
    if not profile:
        # Create empty profile if it doesn't exist
        profile_data = UserProfileCreate(user_id=current_user.id)
        profile = await user_profile_crud.create(db, obj_in=profile_data)
    return profile


@router.put("/me", response_model=UserProfile)
async def update_my_profile(
    profile_in: UserProfileUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update current user's profile.
    """
    profile = await user_profile_crud.get_by_user_id(db, user_id=current_user.id)
    if not profile:
        # Create profile if it doesn't exist
        profile_data = UserProfileCreate(user_id=current_user.id, **profile_in.dict(exclude_unset=True))
        profile = await user_profile_crud.create(db, obj_in=profile_data)
    else:
        profile = await user_profile_crud.update(db, db_obj=profile, obj_in=profile_in)
    return profile


@router.post("/me/avatar")
async def upload_avatar(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Upload user avatar image (max 5MB, JPG/PNG only).
    """
    # Validate file type
    if file.content_type not in ["image/jpeg", "image/jpg", "image/png"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only JPEG and PNG images are allowed"
        )
    
    # Read file content
    content = await file.read()
    
    # Validate file size (5MB limit)
    if len(content) > 5 * 1024 * 1024:  # 5MB
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File size must be less than 5MB"
        )
    
    # Get or create profile
    profile = await user_profile_crud.get_by_user_id(db, user_id=current_user.id)
    if not profile:
        profile_data = UserProfileCreate(user_id=current_user.id)
        profile = await user_profile_crud.create(db, obj_in=profile_data)
    
    # Update avatar
    profile = await user_profile_crud.update_avatar(
        db,
        db_obj=profile,
        avatar_data=content,
        filename=file.filename,
        content_type=file.content_type,
        size=len(content)
    )
    
    return {"message": "Avatar uploaded successfully", "size": len(content)}


@router.post("/me/submit-kyc", response_model=UserProfile)
async def submit_kyc(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Submit KYC for review.
    """
    profile = await user_profile_crud.get_by_user_id(db, user_id=current_user.id)
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Profile not found. Please complete your profile first."
        )
    
    if profile.kyc_status != KYCStatus.PENDING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"KYC is already {profile.kyc_status.value}"
        )
    
    profile = await user_profile_crud.submit_kyc(db, db_obj=profile)
    return profile


# Admin endpoints
@router.get("/{user_id}", response_model=UserProfileWithAvatar, tags=["admin"])
async def get_user_profile(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get user profile by ID. Admin only.
    """
    profile = await user_profile_crud.get_by_user_id(db, user_id=user_id)
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Profile not found"
        )
    return profile


@router.put("/{user_id}/kyc-review", response_model=UserProfile, tags=["admin"])
async def review_kyc(
    user_id: str,
    review_data: KYCReview,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Review and update KYC status. Admin only.
    """
    profile = await user_profile_crud.get_by_user_id(db, user_id=user_id)
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Profile not found"
        )
    
    profile = await user_profile_crud.update_kyc_status(
        db,
        db_obj=profile,
        status=review_data.kyc_status,
        reviewed_by=current_user.id,
        rejection_reason=review_data.kyc_rejection_reason
    )
    return profile
