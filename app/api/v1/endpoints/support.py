from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import (
    get_current_active_user,
    get_current_admin_user,
    get_current_account_manager_user,
)
from app.crud import support_ticket as support_ticket_crud
from app.crud import ticket_message as ticket_message_crud
from app.db.session import get_db
from app.models.user import User
from app.schemas.support import (
    SupportTicket,
    SupportTicketCreate,
    SupportTicketUpdate,
    SupportTicketWithMessages,
    TicketMessage,
    TicketMessageCreate,
)

router = APIRouter()


@router.get("/tickets/me", response_model=List[SupportTicket])
async def read_tickets_me(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get current user's support tickets.
    """
    tickets = await support_ticket_crud.get_by_user_id(
        db, user_id=current_user.id, skip=skip, limit=limit
    )
    return tickets


@router.post("/tickets", response_model=SupportTicket)
async def create_ticket(
    ticket_in: SupportTicketCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Create a new support ticket.
    """
    ticket_data = {
        "user_id": current_user.id,
        "subject": ticket_in.subject,
        "description": ticket_in.description,
        "priority": ticket_in.priority,
    }
    ticket = await support_ticket_crud.create(db, obj_in=ticket_data)
    return ticket


@router.get("/tickets/{ticket_id}", response_model=SupportTicketWithMessages)
async def read_ticket(
    ticket_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get a support ticket by ID with messages.
    """
    ticket = await support_ticket_crud.get_with_messages(db, ticket_id=ticket_id)
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Ticket not found"
        )
    if (
        ticket.user_id != current_user.id
        and ticket.account_manager_id != current_user.id
        and current_user.role != "admin"
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
        )
    return ticket


@router.put("/tickets/{ticket_id}", response_model=SupportTicket)
async def update_ticket(
    ticket_id: str,
    ticket_in: SupportTicketUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update a support ticket.
    """
    ticket = await support_ticket_crud.get(db, id=ticket_id)
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Ticket not found"
        )
    if (
        ticket.user_id != current_user.id
        and ticket.account_manager_id != current_user.id
        and current_user.role != "admin"
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
        )
    ticket = await support_ticket_crud.update(db, db_obj=ticket, obj_in=ticket_in)
    return ticket


@router.post("/tickets/{ticket_id}/messages", response_model=TicketMessage)
async def create_ticket_message(
    ticket_id: str,
    message_in: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Create a new ticket message.
    """
    ticket = await support_ticket_crud.get(db, id=ticket_id)
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Ticket not found"
        )
    if (
        ticket.user_id != current_user.id
        and ticket.account_manager_id != current_user.id
        and current_user.role != "admin"
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
        )
    message_data = {
        "ticket_id": ticket_id,
        "sender_id": current_user.id,
        "message": message_in,
    }
    message = await ticket_message_crud.create(db, obj_in=message_data)
    return message


@router.get("/tickets", response_model=List[SupportTicket], tags=["admin"])
async def read_tickets(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_account_manager_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get all support tickets. Admin or account manager only.
    """
    if current_user.role == "admin":
        tickets = await support_ticket_crud.get_multi(db, skip=skip, limit=limit)
    else:
        tickets = await support_ticket_crud.get_by_account_manager_id(
            db, account_manager_id=current_user.id, skip=skip, limit=limit
        )
    return tickets


@router.post("/tickets/{ticket_id}/assign/{account_manager_id}", response_model=SupportTicket, tags=["admin"])
async def assign_ticket(
    ticket_id: str,
    account_manager_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Assign a ticket to an account manager. Admin only.
    """
    ticket = await support_ticket_crud.get(db, id=ticket_id)
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Ticket not found"
        )
    ticket = await support_ticket_crud.assign_account_manager(
        db, ticket_id=ticket_id, account_manager_id=account_manager_id
    )
    return ticket
