from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, wallets, transactions, support, password_reset, profiles, admin
from app.api.v1.endpoints import admin_profiles, documents

api_router = APIRouter()

api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])
api_router.include_router(users.router, prefix="/users", tags=["Users"])
api_router.include_router(profiles.router, prefix="/profiles", tags=["User Profiles"])
api_router.include_router(wallets.router, prefix="/wallets", tags=["Wallets"])
api_router.include_router(transactions.router, prefix="/transactions", tags=["Transactions"])
api_router.include_router(support.router, prefix="/support", tags=["Support"])
api_router.include_router(password_reset.router, prefix="/password-reset", tags=["Password Reset"])
api_router.include_router(admin.router, prefix="/admin", tags=["Admin"])
api_router.include_router(admin_profiles.router, prefix="/admin/profiles", tags=["Admin Profile Management"])  # Temporarily disabled
api_router.include_router(documents.router, prefix="/documents", tags=["Document Management"])  # Temporarily disabled
