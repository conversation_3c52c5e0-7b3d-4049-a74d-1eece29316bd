from typing import Any, Dict, Optional

from pydantic import BaseSettings, EmailStr, PostgresDsn, validator, AnyUrl


class Settings(BaseSettings):
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

    # Application settings
    APP_NAME: str
    ENVIRONMENT: str
    DEBUG: bool
    API_V1_STR: str
    SECRET_KEY: str
    ALGORITHM: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int
    REFRESH_TOKEN_EXPIRE_DAYS: int

    # Database settings
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_DB: str
    POSTGRES_HOST: str
    POSTGRES_PORT: str
    DATABASE_URL: Optional[str] = None

    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme="postgresql+asyncpg",
            user=values.get("POSTGRES_USER"),
            password=values.get("POSTGRES_PASSWORD"),
            host=values.get("POSTGRES_HOST"),
            port=values.get("POSTGRES_PORT"),
            path=f"/{values.get('POSTGRES_DB') or ''}",
        )

    # Initial superuser
    CREATE_INITIAL_SUPERUSER: bool = False
    INITIAL_SUPERUSER_EMAIL: Optional[EmailStr] = None
    INITIAL_SUPERUSER_PASSWORD: Optional[str] = None

    # Logging
    LOG_LEVEL: str = "INFO"

    # Frontend URL (for email links)
    FRONTEND_URL: str = "http://localhost:3000"

    # Password reset
    PASSWORD_RESET_TOKEN_EXPIRE_MINUTES: int = 30


settings = Settings()
