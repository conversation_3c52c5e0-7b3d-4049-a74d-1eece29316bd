from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.crud.base import CRUDBase
from app.models.user_profile import UserProfile, KYCStatus
from app.schemas.user_profile import UserProfileCreate, UserProfileUpdate


class CRUDUserProfile(CRUDBase[UserProfile, UserProfileCreate, UserProfileUpdate]):
    async def get_by_user_id(self, db: AsyncSession, *, user_id: str) -> Optional[UserProfile]:
        """
        Get a user profile by user ID.
        """
        result = await db.execute(select(UserProfile).where(UserProfile.user_id == user_id))
        return result.scalars().first()

    async def update_kyc_status(
        self, 
        db: AsyncSession, 
        *, 
        db_obj: UserProfile, 
        status: KYCStatus,
        reviewed_by: str,
        rejection_reason: Optional[str] = None
    ) -> UserProfile:
        """
        Update KYC status of a user profile.
        """
        from datetime import datetime
        
        db_obj.kyc_status = status
        db_obj.kyc_reviewed_by = reviewed_by
        db_obj.kyc_reviewed_at = datetime.utcnow()
        
        if status == KYCStatus.REJECTED and rejection_reason:
            db_obj.kyc_rejection_reason = rejection_reason
        else:
            db_obj.kyc_rejection_reason = None
            
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update_avatar(
        self,
        db: AsyncSession,
        *,
        db_obj: UserProfile,
        avatar_data: bytes,
        filename: str,
        content_type: str,
        size: int
    ) -> UserProfile:
        """
        Update user avatar image.
        """
        db_obj.avatar_image = avatar_data
        db_obj.avatar_image_filename = filename
        db_obj.avatar_image_content_type = content_type
        db_obj.avatar_image_size = size
        
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def submit_kyc(self, db: AsyncSession, *, db_obj: UserProfile) -> UserProfile:
        """
        Submit KYC for review.
        """
        from datetime import datetime
        
        db_obj.kyc_status = KYCStatus.UNDER_REVIEW
        db_obj.kyc_submitted_at = datetime.utcnow()
        
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj


user_profile = CRUDUserProfile(UserProfile)
