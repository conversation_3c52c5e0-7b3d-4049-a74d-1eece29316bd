"""
CRUD operations for documents.
"""
from datetime import datetime
from typing import List, Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func

from app.crud.base import CRUDBase
from app.models.document import Document, DocumentType, DocumentStatus
from app.schemas.document import DocumentUpload


class CRUDDocument(CRUDBase[Document, DocumentUpload, dict]):
    """CRUD operations for documents."""
    
    async def create_document(
        self,
        db: AsyncSession,
        *,
        user_id: str,
        document_data: DocumentUpload,
        file_data: bytes,
        upload_ip: Optional[str] = None
    ) -> Document:
        """
        Create a new document with file data.
        """
        db_obj = Document(
            user_id=user_id,
            document_type=document_data.document_type,
            filename=document_data.filename,
            content_type=document_data.content_type,
            file_size=len(file_data),
            file_data=file_data,
            upload_ip=upload_ip,
            status=DocumentStatus.PENDING
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def get_by_user_id(
        self,
        db: AsyncSession,
        *,
        user_id: str,
        document_type: Optional[DocumentType] = None
    ) -> List[Document]:
        """
        Get all documents for a user, optionally filtered by document type.
        """
        query = select(Document).where(Document.user_id == user_id)
        
        if document_type:
            query = query.where(Document.document_type == document_type)
        
        query = query.order_by(Document.created_at.desc())
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_by_status(
        self,
        db: AsyncSession,
        *,
        status: DocumentStatus,
        skip: int = 0,
        limit: int = 100
    ) -> List[Document]:
        """
        Get documents by status.
        """
        query = select(Document).where(Document.status == status)
        query = query.order_by(Document.created_at.desc())
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def verify_document(
        self,
        db: AsyncSession,
        *,
        document_id: str,
        status: DocumentStatus,
        reviewed_by: str,
        rejection_reason: Optional[str] = None
    ) -> Optional[Document]:
        """
        Update document verification status.
        """
        document = await self.get(db, id=document_id)
        if not document:
            return None
        
        document.status = status
        document.reviewed_by = reviewed_by
        document.reviewed_at = datetime.now()
        
        if status == DocumentStatus.REJECTED:
            document.rejection_reason = rejection_reason
        else:
            document.rejection_reason = None
        
        db.add(document)
        await db.commit()
        await db.refresh(document)
        return document
    
    async def get_user_document_summary(
        self,
        db: AsyncSession,
        *,
        user_id: str
    ) -> dict:
        """
        Get summary of user's documents.
        """
        # Get all documents for user
        documents = await self.get_by_user_id(db, user_id=user_id)
        
        # Count by status
        total_documents = len(documents)
        pending_documents = len([d for d in documents if d.status == DocumentStatus.PENDING])
        approved_documents = len([d for d in documents if d.status == DocumentStatus.APPROVED])
        rejected_documents = len([d for d in documents if d.status == DocumentStatus.REJECTED])
        under_review_documents = len([d for d in documents if d.status == DocumentStatus.UNDER_REVIEW])
        
        return {
            "user_id": user_id,
            "total_documents": total_documents,
            "pending_documents": pending_documents,
            "approved_documents": approved_documents,
            "rejected_documents": rejected_documents,
            "under_review_documents": under_review_documents,
            "documents": documents
        }
    
    async def check_document_exists(
        self,
        db: AsyncSession,
        *,
        user_id: str,
        document_type: DocumentType
    ) -> bool:
        """
        Check if user already has a document of the specified type.
        """
        query = select(Document).where(
            Document.user_id == user_id,
            Document.document_type == document_type
        )
        result = await db.execute(query)
        return result.scalars().first() is not None
    
    async def get_pending_documents_count(self, db: AsyncSession) -> int:
        """
        Get count of pending documents for admin dashboard.
        """
        query = select(func.count(Document.id)).where(Document.status == DocumentStatus.PENDING)
        result = await db.execute(query)
        return result.scalar()
    
    async def delete_document(
        self,
        db: AsyncSession,
        *,
        document_id: str,
        user_id: str
    ) -> bool:
        """
        Delete a document (only if it belongs to the user and is not approved).
        """
        document = await self.get(db, id=document_id)
        if not document or document.user_id != user_id:
            return False
        
        # Don't allow deletion of approved documents
        if document.status == DocumentStatus.APPROVED:
            return False
        
        await db.delete(document)
        await db.commit()
        return True


document = CRUDDocument(Document)
