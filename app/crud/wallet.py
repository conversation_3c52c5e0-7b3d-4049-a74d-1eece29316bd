from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.crud.base import CRUDBase
from app.models.wallet import FiatWallet, CryptoWallet
from app.schemas.wallet import FiatWalletCreate, CryptoWalletCreate, WalletUpdate


class CRUDFiatWallet(CRUDBase[FiatWallet, FiatWalletCreate, WalletUpdate]):
    async def get_by_user_id(
        self, db: AsyncSession, *, user_id: str
    ) -> Optional[FiatWallet]:
        """
        Get a fiat wallet by user ID.
        """
        result = await db.execute(select(FiatWallet).where(FiatWallet.user_id == user_id))
        return result.scalars().first()

    async def update_balance(
        self, db: AsyncSession, *, wallet_id: str, amount: float, is_deposit: bool
    ) -> FiatWallet:
        """
        Update a fiat wallet balance.
        """
        wallet = await self.get(db, wallet_id)
        if is_deposit:
            wallet.balance += amount
        else:
            wallet.balance -= amount
        db.add(wallet)
        await db.commit()
        await db.refresh(wallet)
        return wallet


class CRUDCryptoWallet(CRUDBase[CryptoWallet, CryptoWalletCreate, WalletUpdate]):
    async def get_by_user_id(
        self, db: AsyncSession, *, user_id: str
    ) -> Optional[CryptoWallet]:
        """
        Get a crypto wallet by user ID.
        """
        result = await db.execute(
            select(CryptoWallet).where(CryptoWallet.user_id == user_id)
        )
        return result.scalars().first()

    async def get_by_wallet_address(
        self, db: AsyncSession, *, wallet_address: str
    ) -> Optional[CryptoWallet]:
        """
        Get a crypto wallet by wallet address.
        """
        result = await db.execute(
            select(CryptoWallet).where(CryptoWallet.wallet_address == wallet_address)
        )
        return result.scalars().first()

    async def update_balance(
        self, db: AsyncSession, *, wallet_id: str, amount: float, is_deposit: bool
    ) -> CryptoWallet:
        """
        Update a crypto wallet balance.
        """
        wallet = await self.get(db, wallet_id)
        if is_deposit:
            wallet.balance += amount
        else:
            wallet.balance -= amount
        db.add(wallet)
        await db.commit()
        await db.refresh(wallet)
        return wallet

    async def update_wallet_address(
        self, db: AsyncSession, *, db_obj: CryptoWallet, new_address: str
    ) -> CryptoWallet:
        """
        Update crypto wallet address (admin only).
        """
        db_obj.wallet_address = new_address
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj


fiat_wallet = CRUDFiatWallet(FiatWallet)
crypto_wallet = CRUDCryptoWallet(CryptoWallet)
