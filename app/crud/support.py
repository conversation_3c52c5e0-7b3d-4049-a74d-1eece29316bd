from datetime import datetime
from typing import List, Optional

from sqlalchemy import desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from app.crud.base import CRUDBase
from app.models.support import (
    SupportTicket,
    TicketMessage,
    TicketStatus,
    TicketPriority,
)
from app.schemas.support import (
    SupportTicketCreate,
    SupportTicketUpdate,
    TicketMessageCreate,
)


class CRUDSupportTicket(CRUDBase[SupportTicket, SupportTicketCreate, SupportTicketUpdate]):
    async def get_with_messages(
        self, db: AsyncSession, *, ticket_id: int
    ) -> Optional[SupportTicket]:
        """
        Get a support ticket with messages.
        """
        result = await db.execute(
            select(SupportTicket)
            .where(SupportTicket.id == ticket_id)
            .options(
                selectinload(SupportTicket.messages),
                selectinload(SupportTicket.user),
                selectinload(SupportTicket.account_manager),
            )
        )
        return result.scalars().first()

    async def get_by_user_id(
        self, db: AsyncSession, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[SupportTicket]:
        """
        Get support tickets by user ID.
        """
        result = await db.execute(
            select(SupportTicket)
            .where(SupportTicket.user_id == user_id)
            .order_by(desc(SupportTicket.created_at))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_by_account_manager_id(
        self, db: AsyncSession, *, account_manager_id: int, skip: int = 0, limit: int = 100
    ) -> List[SupportTicket]:
        """
        Get support tickets by account manager ID.
        """
        result = await db.execute(
            select(SupportTicket)
            .where(SupportTicket.account_manager_id == account_manager_id)
            .order_by(desc(SupportTicket.created_at))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_by_status(
        self, db: AsyncSession, *, status: TicketStatus, skip: int = 0, limit: int = 100
    ) -> List[SupportTicket]:
        """
        Get support tickets by status.
        """
        result = await db.execute(
            select(SupportTicket)
            .where(SupportTicket.status == status)
            .order_by(desc(SupportTicket.created_at))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def assign_account_manager(
        self, db: AsyncSession, *, ticket_id: int, account_manager_id: int
    ) -> SupportTicket:
        """
        Assign an account manager to a support ticket.
        """
        ticket = await self.get(db, ticket_id)
        ticket.account_manager_id = account_manager_id
        ticket.status = TicketStatus.IN_PROGRESS
        db.add(ticket)
        await db.commit()
        await db.refresh(ticket)
        return ticket

    async def update_status(
        self, db: AsyncSession, *, ticket_id: int, status: TicketStatus
    ) -> SupportTicket:
        """
        Update a support ticket status.
        """
        ticket = await self.get(db, ticket_id)
        ticket.status = status
        if status == TicketStatus.RESOLVED:
            ticket.resolved_at = datetime.now()
        db.add(ticket)
        await db.commit()
        await db.refresh(ticket)
        return ticket


class CRUDTicketMessage(CRUDBase[TicketMessage, TicketMessageCreate, TicketMessageCreate]):
    async def get_by_ticket_id(
        self, db: AsyncSession, *, ticket_id: int, skip: int = 0, limit: int = 100
    ) -> List[TicketMessage]:
        """
        Get ticket messages by ticket ID.
        """
        result = await db.execute(
            select(TicketMessage)
            .where(TicketMessage.ticket_id == ticket_id)
            .order_by(TicketMessage.created_at)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()


support_ticket = CRUDSupportTicket(SupportTicket)
ticket_message = CRUDTicketMessage(TicketMessage)
