import secrets
from datetime import datetime, timedelta
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.crud.base import CRUDBase
from app.models.password_reset import PasswordReset


class CRUDPasswordReset(CRUDBase[PasswordReset, dict, dict]):
    async def create_reset_token(
        self, db: AsyncSession, *, user_id: int, expires_delta: timedelta
    ) -> PasswordReset:
        """
        Create a password reset token.
        """
        # Generate a secure token
        token = secrets.token_urlsafe(32)
        
        # Calculate expiration time
        expires_at = datetime.now() + expires_delta
        
        # Create the password reset record
        db_obj = PasswordReset(
            user_id=user_id,
            token=token,
            expires_at=expires_at,
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def get_by_token(
        self, db: AsyncSession, *, token: str
    ) -> Optional[PasswordReset]:
        """
        Get a password reset by token.
        """
        result = await db.execute(
            select(PasswordReset).where(PasswordReset.token == token)
        )
        return result.scalars().first()
    
    async def mark_as_used(
        self, db: AsyncSession, *, token_id: int
    ) -> PasswordReset:
        """
        Mark a password reset token as used.
        """
        token = await self.get(db, id=token_id)
        token.is_used = True
        db.add(token)
        await db.commit()
        await db.refresh(token)
        return token


password_reset = CRUDPasswordReset(PasswordReset)
