from datetime import datetime
from typing import List, Optional

from sqlalchemy import desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.crud.base import CRUDBase
from app.models.transaction import (
    Transaction,
    TransactionStatus,
    TransactionType,
    WalletType,
)
from app.schemas.transaction import TransactionCreate, TransactionUpdate


class CRUDTransaction(CRUDBase[Transaction, TransactionCreate, TransactionUpdate]):
    async def get_by_user_id(
        self, db: AsyncSession, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[Transaction]:
        """
        Get transactions by user ID.
        """
        result = await db.execute(
            select(Transaction)
            .where(Transaction.user_id == user_id)
            .order_by(desc(Transaction.created_at))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_by_status(
        self, db: AsyncSession, *, status: TransactionStatus, skip: int = 0, limit: int = 100
    ) -> List[Transaction]:
        """
        Get transactions by status.
        """
        result = await db.execute(
            select(Transaction)
            .where(Transaction.status == status)
            .order_by(desc(Transaction.created_at))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_by_type(
        self, db: AsyncSession, *, transaction_type: TransactionType, skip: int = 0, limit: int = 100
    ) -> List[Transaction]:
        """
        Get transactions by type.
        """
        result = await db.execute(
            select(Transaction)
            .where(Transaction.transaction_type == transaction_type)
            .order_by(desc(Transaction.created_at))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_by_wallet_type(
        self, db: AsyncSession, *, wallet_type: WalletType, skip: int = 0, limit: int = 100
    ) -> List[Transaction]:
        """
        Get transactions by wallet type.
        """
        result = await db.execute(
            select(Transaction)
            .where(Transaction.source_wallet_type == wallet_type)
            .order_by(desc(Transaction.created_at))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def update_status(
        self, db: AsyncSession, *, transaction_id: int, status: TransactionStatus
    ) -> Transaction:
        """
        Update a transaction status.
        """
        transaction = await self.get(db, transaction_id)
        transaction.status = status
        if status == TransactionStatus.COMPLETED:
            transaction.completed_at = datetime.now()
        db.add(transaction)
        await db.commit()
        await db.refresh(transaction)
        return transaction


transaction = CRUDTransaction(Transaction)
