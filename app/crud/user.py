from typing import Any, Dict, Optional, Union
from datetime import datetime, timezone

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.core.security import get_password_hash, verify_password
from app.crud.base import CRUDBase
from app.models.user import User
from app.schemas.user import UserC<PERSON>, UserUpdate


class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    async def get_by_email(self, db: AsyncSession, *, email: str) -> Optional[User]:
        """
        Get a user by email.
        """
        result = await db.execute(select(User).where(User.email == email))
        return result.scalars().first()

    async def create(self, db: AsyncSession, *, obj_in: UserCreate) -> User:
        """
        Create a new user.
        """
        db_obj = User(
            email=obj_in.email,
            hashed_password=get_password_hash(obj_in.password),
            first_name=obj_in.first_name,
            last_name=obj_in.last_name,
            is_active=obj_in.is_active,
            role=obj_in.role,
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update(
        self, db: AsyncSession, *, db_obj: User, obj_in: Union[UserUpdate, Dict[str, Any]]
    ) -> User:
        """
        Update a user.
        """
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        if update_data.get("password"):
            hashed_password = get_password_hash(update_data["password"])
            del update_data["password"]
            update_data["hashed_password"] = hashed_password
        return await super().update(db, db_obj=db_obj, obj_in=update_data)

    async def authenticate(
        self, db: AsyncSession, *, email: str, password: str
    ) -> Optional[User]:
        """
        Authenticate a user.
        """
        user = await self.get_by_email(db, email=email)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user

    async def is_active(self, user: User) -> bool:
        """
        Check if a user is active.
        """
        return user.is_active

    async def is_admin(self, user: User) -> bool:
        """
        Check if a user is an admin.
        """
        return user.role == "admin"

    async def is_account_manager(self, user: User) -> bool:
        """
        Check if a user is an account manager.
        """
        return user.role == "account_manager"

    async def update_withdraw_limits(
        self,
        db: AsyncSession,
        *,
        db_obj: User,
        fiat_limit: Optional[float] = None,
        crypto_limit: Optional[float] = None
    ) -> User:
        """
        Update user withdraw limits (admin only).
        """
        if fiat_limit is not None:
            db_obj.fiat_withdraw_limit = fiat_limit
        if crypto_limit is not None:
            db_obj.crypto_withdraw_limit = crypto_limit

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def check_and_reset_daily_limits(self, db: AsyncSession, *, db_obj: User) -> User:
        """
        Check if daily withdraw limits need to be reset and reset them if needed.
        """
        now = datetime.now(timezone.utc)
        reset_date = db_obj.withdraw_limit_reset_date

        # If reset date is not timezone-aware, assume it's UTC
        if reset_date.tzinfo is None:
            reset_date = reset_date.replace(tzinfo=timezone.utc)

        # Check if it's a new day (reset at midnight UTC)
        if now.date() > reset_date.date():
            db_obj.fiat_withdraw_used_today = 0.0
            db_obj.crypto_withdraw_used_today = 0.0
            db_obj.withdraw_limit_reset_date = now

            db.add(db_obj)
            await db.commit()
            await db.refresh(db_obj)

        return db_obj

    async def update_withdraw_usage(
        self,
        db: AsyncSession,
        *,
        db_obj: User,
        amount: float,
        is_fiat: bool
    ) -> User:
        """
        Update withdraw usage after a successful withdrawal.
        """
        # First check and reset daily limits if needed
        db_obj = await self.check_and_reset_daily_limits(db, db_obj=db_obj)

        # Update usage
        if is_fiat:
            db_obj.fiat_withdraw_used_today += amount
        else:
            db_obj.crypto_withdraw_used_today += amount

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_available_withdraw_limits(self, db: AsyncSession, *, db_obj: User) -> dict:
        """
        Get available withdraw limits for a user (after checking daily reset).
        """
        # First check and reset daily limits if needed
        db_obj = await self.check_and_reset_daily_limits(db, db_obj=db_obj)

        return {
            "fiat_limit": db_obj.fiat_withdraw_limit,
            "fiat_used_today": db_obj.fiat_withdraw_used_today,
            "fiat_available": max(0, db_obj.fiat_withdraw_limit - db_obj.fiat_withdraw_used_today),
            "crypto_limit": db_obj.crypto_withdraw_limit,
            "crypto_used_today": db_obj.crypto_withdraw_used_today,
            "crypto_available": max(0, db_obj.crypto_withdraw_limit - db_obj.crypto_withdraw_used_today),
            "reset_date": db_obj.withdraw_limit_reset_date
        }


user = CRUDUser(User)
