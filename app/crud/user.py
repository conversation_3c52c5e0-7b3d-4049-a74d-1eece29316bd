from typing import Any, Dict, Optional, Union

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.core.security import get_password_hash, verify_password
from app.crud.base import CRUDBase
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate


class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    async def get_by_email(self, db: AsyncSession, *, email: str) -> Optional[User]:
        """
        Get a user by email.
        """
        result = await db.execute(select(User).where(User.email == email))
        return result.scalars().first()

    async def create(self, db: AsyncSession, *, obj_in: UserCreate) -> User:
        """
        Create a new user.
        """
        db_obj = User(
            email=obj_in.email,
            hashed_password=get_password_hash(obj_in.password),
            first_name=obj_in.first_name,
            last_name=obj_in.last_name,
            is_active=obj_in.is_active,
            role=obj_in.role,
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update(
        self, db: AsyncSession, *, db_obj: User, obj_in: Union[UserUpdate, Dict[str, Any]]
    ) -> User:
        """
        Update a user.
        """
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        if update_data.get("password"):
            hashed_password = get_password_hash(update_data["password"])
            del update_data["password"]
            update_data["hashed_password"] = hashed_password
        return await super().update(db, db_obj=db_obj, obj_in=update_data)

    async def authenticate(
        self, db: AsyncSession, *, email: str, password: str
    ) -> Optional[User]:
        """
        Authenticate a user.
        """
        user = await self.get_by_email(db, email=email)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user

    async def is_active(self, user: User) -> bool:
        """
        Check if a user is active.
        """
        return user.is_active

    async def is_admin(self, user: User) -> bool:
        """
        Check if a user is an admin.
        """
        return user.role == "admin"

    async def is_account_manager(self, user: User) -> bool:
        """
        Check if a user is an account manager.
        """
        return user.role == "account_manager"

    async def update_withdraw_limits(
        self,
        db: AsyncSession,
        *,
        db_obj: User,
        fiat_limit: Optional[float] = None,
        crypto_limit: Optional[float] = None
    ) -> User:
        """
        Update user withdraw limits (admin only).
        """
        if fiat_limit is not None:
            db_obj.fiat_withdraw_limit = fiat_limit
        if crypto_limit is not None:
            db_obj.crypto_withdraw_limit = crypto_limit

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def reset_withdraw_usage(
        self,
        db: AsyncSession,
        *,
        db_obj: User,
        reset_fiat: bool = False,
        reset_crypto: bool = False
    ) -> User:
        """
        Reset withdraw usage (admin only).
        """
        if reset_fiat:
            db_obj.fiat_withdraw_used = 0.0
        if reset_crypto:
            db_obj.crypto_withdraw_used = 0.0

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update_withdraw_usage(
        self,
        db: AsyncSession,
        *,
        db_obj: User,
        amount: float,
        is_fiat: bool
    ) -> User:
        """
        Update withdraw usage after a successful withdrawal.
        """
        if is_fiat:
            db_obj.fiat_withdraw_used += amount
        else:
            db_obj.crypto_withdraw_used += amount

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_available_withdraw_limits(self, db: AsyncSession, *, user_id: str) -> dict:
        """
        Get available withdraw limits for a user.
        """
        user = await self.get(db, id=user_id)
        if not user:
            return None

        return {
            "fiat_limit": user.fiat_withdraw_limit,
            "fiat_used": user.fiat_withdraw_used,
            "fiat_available": max(0, user.fiat_withdraw_limit - user.fiat_withdraw_used),
            "crypto_limit": user.crypto_withdraw_limit,
            "crypto_used": user.crypto_withdraw_used,
            "crypto_available": max(0, user.crypto_withdraw_limit - user.crypto_withdraw_used),
        }




user = CRUDUser(User)
