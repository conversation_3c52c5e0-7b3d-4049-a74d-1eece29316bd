from typing import List, Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.crud.base import CRUDBase
from app.models.wallet_config import WalletConfig
from app.schemas.wallet_config import WalletConfigCreate, WalletConfigUpdate


class CRUDWalletConfig(CRUDBase[WalletConfig, WalletConfigCreate, WalletConfigUpdate]):
    async def get_by_currency(self, db: AsyncSession, *, currency: str) -> Optional[WalletConfig]:
        """
        Get active wallet config by currency.
        """
        result = await db.execute(
            select(WalletConfig).where(
                WalletConfig.currency == currency,
                WalletConfig.is_active == True
            )
        )
        return result.scalars().first()

    async def get_active_configs(self, db: AsyncSession) -> List[WalletConfig]:
        """
        Get all active wallet configurations.
        """
        result = await db.execute(
            select(WalletConfig).where(WalletConfig.is_active == True)
        )
        return result.scalars().all()

    async def get_by_config_type(
        self, db: AsyncSession, *, config_type: str
    ) -> List[WalletConfig]:
        """
        Get wallet configs by configuration type.
        """
        result = await db.execute(
            select(WalletConfig).where(
                WalletConfig.config_type == config_type,
                WalletConfig.is_active == True
            )
        )
        return result.scalars().all()

    async def deactivate_config(
        self, db: AsyncSession, *, db_obj: WalletConfig
    ) -> WalletConfig:
        """
        Deactivate a wallet configuration.
        """
        db_obj.is_active = False
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj


wallet_config = CRUDWalletConfig(WalletConfig)
