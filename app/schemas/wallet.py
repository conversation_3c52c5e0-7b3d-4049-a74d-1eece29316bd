from datetime import datetime
from typing import Optional

from pydantic import BaseModel


class WalletBase(BaseModel):
    balance: float
    currency: str


class FiatWalletCreate(WalletBase):
    user_id: str
    # Note: Fiat wallets don't have wallet addresses


class CryptoWalletCreate(WalletBase):
    user_id: str
    wallet_address: str


class WalletUpdate(BaseModel):
    balance: Optional[float] = None


class WalletInDBBase(WalletBase):
    id: str
    user_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class FiatWallet(WalletInDBBase):
    # Note: Fiat wallets don't have wallet addresses
    pass


class CryptoWallet(WalletInDBBase):
    wallet_address: str


class CryptoWalletAddressUpdate(BaseModel):
    """Schema for admin to update crypto wallet addresses."""
    wallet_address: str
