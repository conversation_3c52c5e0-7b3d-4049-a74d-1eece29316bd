"""
Document schemas for API requests and responses.
"""
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field

from app.models.document import DocumentType, DocumentStatus


class DocumentUpload(BaseModel):
    """Schema for document upload request."""
    document_type: DocumentType = Field(description="Type of document being uploaded")
    filename: str = Field(description="Original filename")
    content_type: str = Field(description="MIME type (image/jpeg, image/png, application/pdf)")
    
    class Config:
        schema_extra = {
            "example": {
                "document_type": "id_document",
                "filename": "passport.jpg",
                "content_type": "image/jpeg"
            }
        }


class DocumentResponse(BaseModel):
    """Schema for document response (without file data)."""
    id: str
    user_id: str
    document_type: DocumentType
    filename: str
    content_type: str
    file_size: int
    status: DocumentStatus
    reviewed_by: Optional[str] = None
    reviewed_at: Optional[datetime] = None
    rejection_reason: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


class DocumentWithData(DocumentResponse):
    """Schema for document response with file data (admin only)."""
    file_data: bytes


class DocumentVerification(BaseModel):
    """Schema for document verification by admin."""
    status: DocumentStatus = Field(description="New verification status")
    rejection_reason: Optional[str] = Field(None, description="Reason for rejection (required if status is REJECTED)")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "approved",
                "rejection_reason": None
            }
        }


class DocumentSummary(BaseModel):
    """Schema for document summary in user profile."""
    document_type: DocumentType
    status: DocumentStatus
    uploaded_at: datetime
    reviewed_at: Optional[datetime] = None
    
    class Config:
        orm_mode = True


class UserDocumentsSummary(BaseModel):
    """Schema for user's documents summary."""
    user_id: str
    total_documents: int
    pending_documents: int
    approved_documents: int
    rejected_documents: int
    documents: list[DocumentSummary]
    
    class Config:
        schema_extra = {
            "example": {
                "user_id": "123e4567-e89b-12d3-a456-426614174000",
                "total_documents": 3,
                "pending_documents": 1,
                "approved_documents": 2,
                "rejected_documents": 0,
                "documents": [
                    {
                        "document_type": "id_document",
                        "status": "approved",
                        "uploaded_at": "2023-01-01T00:00:00Z",
                        "reviewed_at": "2023-01-02T00:00:00Z"
                    }
                ]
            }
        }
