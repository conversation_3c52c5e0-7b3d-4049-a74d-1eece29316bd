from datetime import datetime
from typing import Optional

from pydantic import BaseModel


class WalletConfigBase(BaseModel):
    config_type: str  # 'default_crypto_address' (only crypto addresses)
    currency: str  # BTC, ETH, etc. (crypto currencies only)
    default_address: str  # Default crypto address
    description: Optional[str] = None
    is_active: bool = True


class WalletConfigCreate(WalletConfigBase):
    """Schema for creating wallet config - created_by and updated_by are set automatically."""
    pass


class WalletConfigUpdate(BaseModel):
    """Schema for updating wallet config - updated_by is set automatically."""
    default_address: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


class WalletConfigInDBBase(WalletConfigBase):
    id: str
    created_by: str
    updated_by: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class WalletConfig(WalletConfigInDBBase):
    pass
