from datetime import datetime
from typing import Optional

from pydantic import BaseModel


class WalletConfigBase(BaseModel):
    config_type: str  # 'default_crypto_address' (only crypto addresses)
    currency: str  # BTC, ETH, etc. (crypto currencies only)
    default_address: str  # Default crypto address
    description: Optional[str] = None
    is_active: bool = True


class WalletConfigCreate(WalletConfigBase):
    created_by: str
    updated_by: str


class WalletConfigUpdate(BaseModel):
    default_address: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    updated_by: str


class WalletConfigInDBBase(WalletConfigBase):
    id: str
    created_by: str
    updated_by: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class WalletConfig(WalletConfigInDBBase):
    pass
