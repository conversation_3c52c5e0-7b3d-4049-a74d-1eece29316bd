from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field

from app.models.transaction import (
    TransactionType,
    TransactionStatus,
    WithdrawalMethod,
    WalletType,
)


class TransactionBase(BaseModel):
    transaction_type: TransactionType
    amount: float = Field(..., gt=0)
    source_wallet_type: WalletType
    destination_wallet_type: Optional[WalletType] = None
    withdrawal_method: Optional[WithdrawalMethod] = None
    description: Optional[str] = None


class TransactionCreate(TransactionBase):
    user_id: str
    fee: Optional[float] = 0.0
    reference_id: Optional[str] = None
    conversion_rate: Optional[float] = None


class TransactionUpdate(BaseModel):
    status: Optional[TransactionStatus] = None
    completed_at: Optional[datetime] = None


class TransactionInDBBase(TransactionBase):
    id: str
    user_id: str
    fee: float
    status: TransactionStatus
    reference_id: Optional[str]
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime]
    conversion_rate: Optional[float]

    class Config:
        orm_mode = True


class Transaction(TransactionInDBBase):
    pass


class DepositCreate(BaseModel):
    amount: float = Field(..., gt=0)
    wallet_type: WalletType
    description: Optional[str] = None


class WithdrawalCreate(BaseModel):
    amount: float = Field(..., gt=0)
    wallet_type: WalletType
    withdrawal_method: WithdrawalMethod
    description: Optional[str] = None


class ConversionCreate(BaseModel):
    amount: float = Field(..., gt=0)
    source_wallet_type: WalletType
    destination_wallet_type: WalletType
    description: Optional[str] = None
