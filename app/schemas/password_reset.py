from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr, Field


class PasswordResetRequest(BaseModel):
    """Schema for requesting a password reset."""
    email: EmailStr


class PasswordResetVerify(BaseModel):
    """Schema for verifying a password reset token."""
    token: str


class PasswordResetConfirm(BaseModel):
    """Schema for confirming a password reset."""
    token: str
    new_password: str = Field(..., min_length=8)


class PasswordResetInDB(BaseModel):
    """Schema for password reset in database."""
    id: str
    user_id: str
    token: str
    is_used: bool
    expires_at: datetime
    created_at: datetime

    class Config:
        orm_mode = True
