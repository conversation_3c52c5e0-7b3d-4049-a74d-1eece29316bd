from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel

from app.models.support import TicketStatus, TicketPriority
from app.schemas.user import User


class TicketMessageBase(BaseModel):
    message: str


class TicketMessageCreate(TicketMessageBase):
    ticket_id: str
    sender_id: str


class TicketMessageInDBBase(TicketMessageBase):
    id: str
    ticket_id: str
    sender_id: str
    created_at: datetime

    class Config:
        orm_mode = True


class TicketMessage(TicketMessageInDBBase):
    sender: User


class SupportTicketBase(BaseModel):
    subject: str
    description: str
    priority: Optional[TicketPriority] = TicketPriority.MEDIUM


class SupportTicketCreate(SupportTicketBase):
    user_id: str


class SupportTicketUpdate(BaseModel):
    subject: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TicketStatus] = None
    priority: Optional[TicketPriority] = None
    account_manager_id: Optional[str] = None
    resolved_at: Optional[datetime] = None


class SupportTicketInDBBase(SupportTicketBase):
    id: str
    user_id: str
    status: TicketStatus
    account_manager_id: Optional[str]
    created_at: datetime
    updated_at: datetime
    resolved_at: Optional[datetime]

    class Config:
        orm_mode = True


class SupportTicket(SupportTicketInDBBase):
    pass


class SupportTicketWithMessages(SupportTicket):
    messages: List[TicketMessage] = []
    user: User
    account_manager: Optional[User] = None
