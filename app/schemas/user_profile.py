from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field

from app.models.user_profile import KYCStatus


class UserProfileBase(BaseModel):
    date_of_birth: Optional[datetime] = None
    phone_number: Optional[str] = None
    address_line_1: Optional[str] = None
    address_line_2: Optional[str] = None
    city: Optional[str] = None
    state_province: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None
    id_document_type: Optional[str] = None
    id_document_number: Optional[str] = None
    id_document_expiry: Optional[datetime] = None


class UserProfileCreate(UserProfileBase):
    user_id: str


class UserProfileUpdate(BaseModel):
    date_of_birth: Optional[datetime] = None
    phone_number: Optional[str] = None
    address_line_1: Optional[str] = None
    address_line_2: Optional[str] = None
    city: Optional[str] = None
    state_province: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None
    id_document_type: Optional[str] = None
    id_document_number: Optional[str] = None
    id_document_expiry: Optional[datetime] = None


class UserProfileInDBBase(UserProfileBase):
    id: str
    user_id: str
    avatar_image_filename: Optional[str] = None
    avatar_image_content_type: Optional[str] = None
    avatar_image_size: Optional[int] = None
    kyc_status: KYCStatus
    kyc_submitted_at: Optional[datetime] = None
    kyc_reviewed_at: Optional[datetime] = None
    kyc_reviewed_by: Optional[str] = None
    kyc_rejection_reason: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class UserProfile(UserProfileInDBBase):
    pass


class UserProfileWithAvatar(UserProfile):
    """Profile with avatar image data for admin use."""
    avatar_image: Optional[bytes] = None


class AvatarUpload(BaseModel):
    """Schema for avatar image upload."""
    filename: str
    content_type: str = Field(..., regex=r"^image/(jpeg|jpg|png)$")
    size: int = Field(..., le=5242880)  # 5MB limit


class KYCReview(BaseModel):
    """Schema for KYC review by admin."""
    kyc_status: KYCStatus
    kyc_rejection_reason: Optional[str] = None


class WithdrawLimitUpdate(BaseModel):
    """Schema for admin to update user withdraw limits."""
    fiat_withdraw_limit: Optional[float] = Field(None, ge=0)
    crypto_withdraw_limit: Optional[float] = Field(None, ge=0)
