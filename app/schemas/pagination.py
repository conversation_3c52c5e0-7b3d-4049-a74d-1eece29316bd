"""
Pagination schemas for Django-style paginated responses.
"""
from typing import List, Optional, Generic, TypeVar
from pydantic import BaseModel, Field
from pydantic.generics import GenericModel

T = TypeVar("T")


class PaginationParams(BaseModel):
    """Query parameters for pagination."""
    page: int = Field(default=1, ge=1, description="Page number (1-based)")
    page_size: int = Field(default=20, ge=1, le=100, description="Number of items per page (max 100)")


class PaginatedResponse(GenericModel, Generic[T]):
    """Django-style paginated response."""
    count: int = Field(description="Total number of items")
    next: Optional[str] = Field(description="URL for next page")
    previous: Optional[str] = Field(description="URL for previous page")
    results: List[T] = Field(description="Current page items")


def create_paginated_response(
    items: List[T],
    total_count: int,
    page: int,
    page_size: int,
    base_url: str,
    query_params: Optional[dict] = None
) -> PaginatedResponse[T]:
    """
    Create a paginated response with next/previous URLs.
    
    Args:
        items: List of items for current page
        total_count: Total number of items across all pages
        page: Current page number (1-based)
        page_size: Number of items per page
        base_url: Base URL for pagination links
        query_params: Additional query parameters to include in URLs
        
    Returns:
        PaginatedResponse with proper next/previous URLs
    """
    total_pages = (total_count + page_size - 1) // page_size  # Ceiling division
    
    # Build query string for additional parameters
    query_string = ""
    if query_params:
        query_parts = [f"{k}={v}" for k, v in query_params.items() if k not in ["page", "page_size"]]
        if query_parts:
            query_string = "&" + "&".join(query_parts)
    
    # Build next URL
    next_url = None
    if page < total_pages:
        next_url = f"{base_url}?page={page + 1}&page_size={page_size}{query_string}"
    
    # Build previous URL
    previous_url = None
    if page > 1:
        previous_url = f"{base_url}?page={page - 1}&page_size={page_size}{query_string}"
    
    return PaginatedResponse[T](
        count=total_count,
        next=next_url,
        previous=previous_url,
        results=items
    )


class PaginationMixin:
    """Mixin class to add pagination functionality to CRUD operations."""
    
    async def get_paginated(
        self,
        db,
        *,
        page: int = 1,
        page_size: int = 20,
        filters: Optional[dict] = None,
        order_by: Optional[str] = None
    ):
        """
        Get paginated results with optional filtering and ordering.
        
        Args:
            db: Database session
            page: Page number (1-based)
            page_size: Number of items per page
            filters: Optional filters to apply
            order_by: Optional field to order by
            
        Returns:
            Tuple of (items, total_count)
        """
        # Calculate offset
        offset = (page - 1) * page_size
        
        # Build base query
        query = db.query(self.model)
        
        # Apply filters if provided
        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field):
                    query = query.filter(getattr(self.model, field) == value)
        
        # Apply ordering if provided
        if order_by and hasattr(self.model, order_by):
            query = query.order_by(getattr(self.model, order_by))
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination
        items = query.offset(offset).limit(page_size).all()
        
        return items, total_count
