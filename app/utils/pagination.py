"""
Pagination utilities for async database operations.
"""
from typing import List, Optional, TypeVar, Tuple, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, text
from sqlalchemy.orm import selectinload

from app.schemas.pagination import PaginatedResponse, create_paginated_response

T = TypeVar("T")


async def paginate_query(
    db: AsyncSession,
    query,
    page: int = 1,
    page_size: int = 20,
    base_url: str = "",
    query_params: Optional[dict] = None
) -> PaginatedResponse[Any]:
    """
    Paginate a SQLAlchemy query for async operations.
    
    Args:
        db: Async database session
        query: SQLAlchemy query to paginate
        page: Page number (1-based)
        page_size: Number of items per page
        base_url: Base URL for pagination links
        query_params: Additional query parameters
        
    Returns:
        PaginatedResponse with paginated results
    """
    # Calculate offset
    offset = (page - 1) * page_size
    
    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    count_result = await db.execute(count_query)
    total_count = count_result.scalar()
    
    # Get paginated results
    paginated_query = query.offset(offset).limit(page_size)
    result = await db.execute(paginated_query)
    items = result.scalars().all()
    
    return create_paginated_response(
        items=items,
        total_count=total_count,
        page=page,
        page_size=page_size,
        base_url=base_url,
        query_params=query_params
    )


async def paginate_users(
    db: AsyncSession,
    page: int = 1,
    page_size: int = 20,
    role_filter: Optional[str] = None,
    base_url: str = "",
    query_params: Optional[dict] = None
) -> PaginatedResponse[Any]:
    """
    Paginate users with optional role filtering.
    
    Args:
        db: Async database session
        page: Page number (1-based)
        page_size: Number of items per page
        role_filter: Optional role to filter by
        base_url: Base URL for pagination links
        query_params: Additional query parameters
        
    Returns:
        PaginatedResponse with paginated user results
    """
    from app.models.user import User
    
    # Build query
    query = select(User)
    
    # Apply role filter if provided
    if role_filter:
        query = query.where(User.role == role_filter)
    
    # Order by created_at descending (newest first)
    query = query.order_by(User.created_at.desc())
    
    return await paginate_query(
        db=db,
        query=query,
        page=page,
        page_size=page_size,
        base_url=base_url,
        query_params=query_params
    )


async def paginate_transactions(
    db: AsyncSession,
    user_id: Optional[str] = None,
    page: int = 1,
    page_size: int = 20,
    base_url: str = "",
    query_params: Optional[dict] = None
) -> PaginatedResponse[Any]:
    """
    Paginate transactions with optional user filtering.
    
    Args:
        db: Async database session
        user_id: Optional user ID to filter by
        page: Page number (1-based)
        page_size: Number of items per page
        base_url: Base URL for pagination links
        query_params: Additional query parameters
        
    Returns:
        PaginatedResponse with paginated transaction results
    """
    from app.models.transaction import Transaction
    
    # Build query
    query = select(Transaction)
    
    # Apply user filter if provided
    if user_id:
        query = query.where(Transaction.user_id == user_id)
    
    # Order by created_at descending (newest first)
    query = query.order_by(Transaction.created_at.desc())
    
    return await paginate_query(
        db=db,
        query=query,
        page=page,
        page_size=page_size,
        base_url=base_url,
        query_params=query_params
    )


async def paginate_user_profiles(
    db: AsyncSession,
    page: int = 1,
    page_size: int = 20,
    kyc_status: Optional[str] = None,
    base_url: str = "",
    query_params: Optional[dict] = None
) -> PaginatedResponse[Any]:
    """
    Paginate user profiles with optional KYC status filtering.
    
    Args:
        db: Async database session
        page: Page number (1-based)
        page_size: Number of items per page
        kyc_status: Optional KYC status to filter by
        base_url: Base URL for pagination links
        query_params: Additional query parameters
        
    Returns:
        PaginatedResponse with paginated profile results
    """
    from app.models.user_profile import UserProfile
    
    # Build query with user relationship
    query = select(UserProfile).options(selectinload(UserProfile.user))
    
    # Apply KYC status filter if provided
    if kyc_status:
        query = query.where(UserProfile.kyc_status == kyc_status)
    
    # Order by updated_at descending (most recently updated first)
    query = query.order_by(UserProfile.updated_at.desc())
    
    return await paginate_query(
        db=db,
        query=query,
        page=page,
        page_size=page_size,
        base_url=base_url,
        query_params=query_params
    )
