"""
Simple in-memory rate limiter for IP-based rate limiting.
"""
import time
from typing import Dict, List
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, Request, status


class InMemoryRateLimiter:
    """
    Simple in-memory rate limiter that tracks requests per IP address.
    """
    
    def __init__(self):
        # Dictionary to store request timestamps per IP
        # Format: {ip_address: [timestamp1, timestamp2, ...]}
        self._requests: Dict[str, List[float]] = {}
    
    def is_allowed(self, ip_address: str, max_requests: int, window_seconds: int) -> bool:
        """
        Check if a request from the given IP is allowed.
        
        Args:
            ip_address: The IP address making the request
            max_requests: Maximum number of requests allowed in the time window
            window_seconds: Time window in seconds
            
        Returns:
            True if request is allowed, False otherwise
        """
        current_time = time.time()
        
        # Get existing requests for this IP
        if ip_address not in self._requests:
            self._requests[ip_address] = []
        
        requests = self._requests[ip_address]
        
        # Remove old requests outside the time window
        cutoff_time = current_time - window_seconds
        self._requests[ip_address] = [req_time for req_time in requests if req_time > cutoff_time]
        
        # Check if we're under the limit
        if len(self._requests[ip_address]) < max_requests:
            # Add current request
            self._requests[ip_address].append(current_time)
            return True
        
        return False
    
    def cleanup_old_entries(self, max_age_seconds: int = 3600):
        """
        Clean up old entries to prevent memory leaks.
        Should be called periodically.
        """
        current_time = time.time()
        cutoff_time = current_time - max_age_seconds
        
        # Remove IPs with no recent requests
        ips_to_remove = []
        for ip_address, requests in self._requests.items():
            # Remove old requests
            recent_requests = [req_time for req_time in requests if req_time > cutoff_time]
            if recent_requests:
                self._requests[ip_address] = recent_requests
            else:
                ips_to_remove.append(ip_address)
        
        # Remove IPs with no recent requests
        for ip_address in ips_to_remove:
            del self._requests[ip_address]


# Global rate limiter instance
rate_limiter = InMemoryRateLimiter()


def get_client_ip(request: Request) -> str:
    """
    Get the client IP address from the request.
    Handles X-Forwarded-For header for proxy scenarios.
    """
    # Check for X-Forwarded-For header (common with proxies/load balancers)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # Take the first IP in the chain
        return forwarded_for.split(",")[0].strip()
    
    # Check for X-Real-IP header
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip.strip()
    
    # Fall back to direct client IP
    return request.client.host if request.client else "unknown"


def check_rate_limit(
    request: Request,
    max_requests: int = 3,
    window_seconds: int = 1800,  # 30 minutes
    error_message: str = "Rate limit exceeded. Please try again later."
):
    """
    Check rate limit for the current request and raise HTTPException if exceeded.
    
    Args:
        request: FastAPI Request object
        max_requests: Maximum number of requests allowed
        window_seconds: Time window in seconds
        error_message: Error message to return if rate limit is exceeded
        
    Raises:
        HTTPException: If rate limit is exceeded
    """
    client_ip = get_client_ip(request)
    
    if not rate_limiter.is_allowed(client_ip, max_requests, window_seconds):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=error_message,
            headers={"Retry-After": str(window_seconds)}
        )
    
    # Periodically clean up old entries (1% chance per request)
    import random
    if random.random() < 0.01:
        rate_limiter.cleanup_old_entries()
