"""Add withdraw usage tracking fields

Revision ID: 9747a6b481c0
Revises: 8e58560c5f35
Create Date: 2025-05-24 13:55:19.961596

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision = '9747a6b481c0'
down_revision = '8e58560c5f35'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('fiat_withdraw_used', sa.Float(), nullable=False))
    op.add_column('users', sa.Column('crypto_withdraw_used', sa.Float(), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'crypto_withdraw_used')
    op.drop_column('users', 'fiat_withdraw_used')
    # ### end Alembic commands ###
