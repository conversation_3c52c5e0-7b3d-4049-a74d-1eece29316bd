"""Add documents table for KYC verification

Revision ID: 81d9c16c5087
Revises: 9747a6b481c0
Create Date: 2025-05-24 18:12:07.835738

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision = '81d9c16c5087'
down_revision = '9747a6b481c0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
