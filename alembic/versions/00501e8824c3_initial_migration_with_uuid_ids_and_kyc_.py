"""Initial migration with UUID IDs and KYC features

Revision ID: 00501e8824c3
Revises:
Create Date: 2025-05-24 11:27:26.520336

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision = '00501e8824c3'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Create users table
    op.create_table('users',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('email', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('first_name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('last_name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('role', sa.<PERSON>um('ADMIN', 'USER', 'ACCOUNT_MANAGER', name='userrole'), nullable=False),
        sa.Column('hashed_password', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('fiat_withdraw_limit', sa.Float(), nullable=False),
        sa.Column('crypto_withdraw_limit', sa.Float(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)

    # Create user_profiles table
    op.create_table('user_profiles',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('date_of_birth', sa.DateTime(), nullable=True),
        sa.Column('phone_number', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('address_line_1', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('address_line_2', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('city', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('state_province', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('postal_code', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('country', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('id_document_type', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('id_document_number', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('id_document_expiry', sa.DateTime(), nullable=True),
        sa.Column('avatar_image', sa.LargeBinary(), nullable=True),
        sa.Column('avatar_image_filename', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('avatar_image_content_type', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('avatar_image_size', sa.Integer(), nullable=True),
        sa.Column('kyc_status', sa.Enum('PENDING', 'UNDER_REVIEW', 'APPROVED', 'REJECTED', name='kycstatus'), nullable=False),
        sa.Column('kyc_submitted_at', sa.DateTime(), nullable=True),
        sa.Column('kyc_reviewed_at', sa.DateTime(), nullable=True),
        sa.Column('kyc_reviewed_by', sa.String(), nullable=True),
        sa.Column('kyc_rejection_reason', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['kyc_reviewed_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id')
    )

    # Create wallet_configs table
    op.create_table('wallet_configs',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('config_type', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('currency', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('default_address', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('created_by', sa.String(), nullable=False),
        sa.Column('updated_by', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_wallet_configs_config_type'), 'wallet_configs', ['config_type'], unique=False)
    op.create_index(op.f('ix_wallet_configs_currency'), 'wallet_configs', ['currency'], unique=False)

    # Create fiat_wallets table
    op.create_table('fiat_wallets',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('balance', sa.Float(), nullable=False),
        sa.Column('currency', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('wallet_address', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id')
    )

    # Create crypto_wallets table
    op.create_table('crypto_wallets',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('balance', sa.Float(), nullable=False),
        sa.Column('currency', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('wallet_address', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id')
    )
    op.create_index(op.f('ix_crypto_wallets_wallet_address'), 'crypto_wallets', ['wallet_address'], unique=False)

    # Create transactions table
    op.create_table('transactions',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('transaction_type', sa.Enum('DEPOSIT', 'WITHDRAWAL', 'CONVERSION', name='transactiontype'), nullable=False),
        sa.Column('amount', sa.Float(), nullable=False),
        sa.Column('fee', sa.Float(), nullable=False),
        sa.Column('source_wallet_type', sa.Enum('FIAT', 'CRYPTO', name='wallettype'), nullable=False),
        sa.Column('destination_wallet_type', sa.Enum('FIAT', 'CRYPTO', name='wallettype'), nullable=True),
        sa.Column('status', sa.Enum('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED', name='transactionstatus'), nullable=False),
        sa.Column('withdrawal_method', sa.Enum('WIRE_TRANSFER', 'CARD', 'CRYPTO', name='withdrawalmethod'), nullable=True),
        sa.Column('reference_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('conversion_rate', sa.Float(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_transactions_reference_id'), 'transactions', ['reference_id'], unique=False)

    # Create support_tickets table
    op.create_table('support_tickets',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('subject', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('status', sa.Enum('OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED', name='ticketstatus'), nullable=False),
        sa.Column('priority', sa.Enum('LOW', 'MEDIUM', 'HIGH', 'URGENT', name='ticketpriority'), nullable=False),
        sa.Column('account_manager_id', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('resolved_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['account_manager_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create ticket_messages table
    op.create_table('ticket_messages',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('ticket_id', sa.String(), nullable=False),
        sa.Column('sender_id', sa.String(), nullable=False),
        sa.Column('message', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['sender_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['ticket_id'], ['support_tickets.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create password_resets table
    op.create_table('password_resets',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('token', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('is_used', sa.Boolean(), nullable=False),
        sa.Column('expires_at', sa.DateTime(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_password_resets_token'), 'password_resets', ['token'], unique=False)


def downgrade():
    # Drop tables in reverse order
    op.drop_index(op.f('ix_password_resets_token'), table_name='password_resets')
    op.drop_table('password_resets')
    op.drop_table('ticket_messages')
    op.drop_table('support_tickets')
    op.drop_index(op.f('ix_transactions_reference_id'), table_name='transactions')
    op.drop_table('transactions')
    op.drop_index(op.f('ix_crypto_wallets_wallet_address'), table_name='crypto_wallets')
    op.drop_table('crypto_wallets')
    op.drop_table('fiat_wallets')
    op.drop_index(op.f('ix_wallet_configs_currency'), table_name='wallet_configs')
    op.drop_index(op.f('ix_wallet_configs_config_type'), table_name='wallet_configs')
    op.drop_table('wallet_configs')
    op.drop_table('user_profiles')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
