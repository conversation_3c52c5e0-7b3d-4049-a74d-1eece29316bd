"""Remove wallet_address from fiat_wallets - only crypto needs addresses

Revision ID: 8e58560c5f35
Revises: 00501e8824c3
Create Date: 2025-05-24 11:35:18.251290

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision = '8e58560c5f35'
down_revision = '00501e8824c3'
branch_labels = None
depends_on = None


def upgrade():
    # Remove wallet_address column from fiat_wallets table
    # SQLite doesn't support DROP COLUMN, so we need to recreate the table
    op.execute("""
        CREATE TABLE fiat_wallets_new (
            id VARCHAR NOT NULL,
            user_id VARCHAR NOT NULL,
            balance FLOAT NOT NULL,
            currency VARCHAR NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            PRIMARY KEY (id),
            FOREIGN KEY(user_id) REFERENCES users (id),
            UNIQUE (user_id)
        )
    """)

    # Copy data from old table to new table (excluding wallet_address)
    op.execute("""
        INSERT INTO fiat_wallets_new (id, user_id, balance, currency, created_at, updated_at)
        SELECT id, user_id, balance, currency, created_at, updated_at
        FROM fiat_wallets
    """)

    # Drop old table and rename new table
    op.drop_table('fiat_wallets')
    op.execute("ALTER TABLE fiat_wallets_new RENAME TO fiat_wallets")


def downgrade():
    # Add wallet_address column back to fiat_wallets table
    # SQLite doesn't support ADD COLUMN with constraints, so we recreate the table
    op.execute("""
        CREATE TABLE fiat_wallets_new (
            id VARCHAR NOT NULL,
            user_id VARCHAR NOT NULL,
            balance FLOAT NOT NULL,
            currency VARCHAR NOT NULL,
            wallet_address VARCHAR,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            PRIMARY KEY (id),
            FOREIGN KEY(user_id) REFERENCES users (id),
            UNIQUE (user_id)
        )
    """)

    # Copy data from old table to new table
    op.execute("""
        INSERT INTO fiat_wallets_new (id, user_id, balance, currency, wallet_address, created_at, updated_at)
        SELECT id, user_id, balance, currency, NULL, created_at, updated_at
        FROM fiat_wallets
    """)

    # Drop old table and rename new table
    op.drop_table('fiat_wallets')
    op.execute("ALTER TABLE fiat_wallets_new RENAME TO fiat_wallets")
