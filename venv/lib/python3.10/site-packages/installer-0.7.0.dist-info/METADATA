Metadata-Version: 2.1
Name: installer
Version: 0.7.0
Summary: A library for installing Python wheels.
Author-email: <PERSON><PERSON><PERSON> <<EMAIL>>
Requires-Python: >=3.7
Description-Content-Type: text/markdown
Classifier: License :: OSI Approved :: MIT License
Project-URL: GitHub, https://github.com/pypa/installer

# installer

<!-- start readme-pitch -->

This is a low-level library for installing a Python package from a
[wheel distribution](https://packaging.python.org/glossary/#term-Wheel). It
provides basic functionality and abstractions for handling wheels and installing
packages from wheels.

- Logic for "unpacking" a wheel (i.e. installation).
- Abstractions for various parts of the unpacking process.
- Extensible simple implementations of the abstractions.
- Platform-independent Python script wrapper generation.

<!-- end readme-pitch -->

You can read more in the [documentation](https://installer.rtfd.io/).

