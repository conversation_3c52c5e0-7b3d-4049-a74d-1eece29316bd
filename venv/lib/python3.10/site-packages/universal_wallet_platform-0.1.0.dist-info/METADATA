Metadata-Version: 2.3
Name: universal-wallet-platform
Version: 0.1.0
Summary: A fintech application for managing dual wallet systems (Fiat and Cryptocurrency)
Author: Your Name
Author-email: <EMAIL>
Requires-Python: >=3.9,<4.0
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Dist: alembic (>=1.12.0,<2.0.0)
Requires-Dist: asyncpg (>=0.28.0,<0.29.0)
Requires-Dist: fastapi (>=0.104.0,<0.105.0)
Requires-Dist: httpx (>=0.25.0,<0.26.0)
Requires-Dist: loguru (>=0.7.2,<0.8.0)
Requires-Dist: passlib[bcrypt] (>=1.7.4,<2.0.0)
Requires-Dist: pydantic[dotenv,email] (==1.10.12)
Requires-Dist: python-dateutil (>=2.8.2,<3.0.0)
Requires-Dist: python-jose[cryptography] (>=3.3.0,<4.0.0)
Requires-Dist: python-multipart (>=0.0.6,<0.0.7)
Requires-Dist: sqlmodel (==0.0.8)
Requires-Dist: tenacity (>=8.2.3,<9.0.0)
Requires-Dist: uvicorn (>=0.23.2,<0.24.0)
Description-Content-Type: text/markdown

# Universal Wallet Platform

A fintech application designed to showcase the integration and management of dual wallet systems—Fiat and Cryptocurrency (Bitcoin)—within a single user-friendly ecosystem.

## Features

- **Dual Wallet System**: Manage both fiat and cryptocurrency (Bitcoin) wallets
- **User Authentication**: Secure OAuth2 authentication with JWT tokens
- **Transaction Management**: Deposits, withdrawals, and conversions between wallets
- **Support Ticket System**: Create and manage support tickets with account manager assignment
- **Admin Dashboard**: Manage users, wallets, transactions, and support tickets
- **Password Reset**: Secure password reset functionality with time-limited tokens

## Tech Stack

- **FastAPI**: Modern, fast web framework for building APIs
- **SQLModel**: ORM for interacting with the database
- **Alembic**: Database migration tool
- **PostgreSQL**: Relational database
- **Pydantic**: Data validation and settings management
- **Poetry**: Dependency management
- **Docker**: Containerization
- **Pytest**: Testing framework

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Python 3.9+
- Poetry (optional for local development)

### Setup

1. Clone the repository:

```bash
git clone https://github.com/yourusername/universal-wallet-platform.git
cd universal-wallet-platform
```

2. Create a `.env` file based on the `.env.example`:

```bash
cp .env.example .env
```

3. Start the application with Docker Compose:

```bash
docker-compose up -d
```

4. The API will be available at http://localhost:8000/api/v1/docs

### Local Development

1. Install dependencies:

```bash
poetry install
```

2. Set up the database:

```bash
alembic upgrade head
```

3. Run the application:

```bash
uvicorn app.main:app --reload
```

## API Documentation

Once the application is running, you can access the API documentation at:

- Swagger UI: http://localhost:8000/api/v1/docs
- ReDoc: http://localhost:8000/api/v1/redoc

## Testing

The project includes comprehensive test coverage with 40+ tests covering:

- Authentication and security
- Wallet operations
- Transaction logic
- Password reset functionality
- API endpoints and error handling

Run the tests with pytest:

```bash
poetry run pytest
```

Run tests with coverage:

```bash
poetry run pytest --cov=app --cov-report=html
```

## Project Structure

```
universal-wallet-platform/
├── alembic/                  # Database migrations
├── app/                      # Application code
│   ├── api/                  # API routes
│   │   └── v1/               # API version 1
│   │       └── endpoints/    # API endpoints
│   ├── core/                 # Core functionality
│   ├── crud/                 # Database operations
│   ├── db/                   # Database connection
│   ├── models/               # SQLModel models
│   ├── schemas/              # Pydantic schemas
│   ├── services/             # Business logic
│   └── utils/                # Utility functions
├── scripts/                  # Scripts for deployment
├── tests/                    # Tests
├── .env.example              # Example environment variables
├── docker-compose.yml        # Docker Compose configuration
├── Dockerfile                # Docker configuration
├── pyproject.toml            # Poetry configuration
└── README.md                 # Project documentation
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

