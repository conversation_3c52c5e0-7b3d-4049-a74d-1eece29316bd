"""
File contains the standard library of Python 2.7.

DO NOT EDIT. If the standard library changes, a new list should be created
using the mkstdlibs.py script.
"""

stdlib = {
    "AL",
    "BaseHTTPServer",
    "Bastion",
    "CGIHTTPServer",
    "Carbon",
    "ColorPicker",
    "ConfigParser",
    "<PERSON><PERSON>",
    "DEVICE",
    "DocXMLRPCServer",
    "EasyDialogs",
    "FL",
    "FrameWork",
    "GL",
    "HTMLParser",
    "MacOS",
    "MimeWriter",
    "MiniAEFrame",
    "Nav",
    "PixMapWrapper",
    "Queue",
    "SUNAUDIODEV",
    "ScrolledText",
    "SimpleHTTPServer",
    "SimpleXMLRPCServer",
    "SocketServer",
    "StringIO",
    "Tix",
    "Tkinter",
    "UserDict",
    "UserList",
    "UserString",
    "W",
    "__builtin__",
    "_ast",
    "_winreg",
    "abc",
    "aepack",
    "aetools",
    "aetypes",
    "aifc",
    "al",
    "anydbm",
    "applesingle",
    "argparse",
    "array",
    "ast",
    "asynchat",
    "asyncore",
    "atexit",
    "audioop",
    "autoGIL",
    "base64",
    "bdb",
    "binascii",
    "binhex",
    "bisect",
    "bsddb",
    "buildtools",
    "bz2",
    "cPickle",
    "cProfile",
    "cStringIO",
    "calendar",
    "cd",
    "cfmfile",
    "cgi",
    "cgitb",
    "chunk",
    "cmath",
    "cmd",
    "code",
    "codecs",
    "codeop",
    "collections",
    "colorsys",
    "commands",
    "compileall",
    "compiler",
    "contextlib",
    "cookielib",
    "copy",
    "copy_reg",
    "crypt",
    "csv",
    "ctypes",
    "curses",
    "datetime",
    "dbhash",
    "dbm",
    "decimal",
    "difflib",
    "dircache",
    "dis",
    "distutils",
    "dl",
    "doctest",
    "dumbdbm",
    "dummy_thread",
    "dummy_threading",
    "email",
    "encodings",
    "ensurepip",
    "errno",
    "exceptions",
    "fcntl",
    "filecmp",
    "fileinput",
    "findertools",
    "fl",
    "flp",
    "fm",
    "fnmatch",
    "formatter",
    "fpectl",
    "fpformat",
    "fractions",
    "ftplib",
    "functools",
    "future_builtins",
    "gc",
    "gdbm",
    "gensuitemodule",
    "getopt",
    "getpass",
    "gettext",
    "gl",
    "glob",
    "grp",
    "gzip",
    "hashlib",
    "heapq",
    "hmac",
    "hotshot",
    "htmlentitydefs",
    "htmllib",
    "httplib",
    "ic",
    "icopen",
    "imageop",
    "imaplib",
    "imgfile",
    "imghdr",
    "imp",
    "importlib",
    "imputil",
    "inspect",
    "io",
    "itertools",
    "jpeg",
    "json",
    "keyword",
    "lib2to3",
    "linecache",
    "locale",
    "logging",
    "macerrors",
    "macostools",
    "macpath",
    "macresource",
    "mailbox",
    "mailcap",
    "marshal",
    "math",
    "md5",
    "mhlib",
    "mimetools",
    "mimetypes",
    "mimify",
    "mmap",
    "modulefinder",
    "msilib",
    "msvcrt",
    "multifile",
    "multiprocessing",
    "mutex",
    "netrc",
    "new",
    "nis",
    "nntplib",
    "ntpath",
    "numbers",
    "operator",
    "optparse",
    "os",
    "ossaudiodev",
    "parser",
    "pdb",
    "pickle",
    "pickletools",
    "pipes",
    "pkgutil",
    "platform",
    "plistlib",
    "popen2",
    "poplib",
    "posix",
    "posixfile",
    "posixpath",
    "pprint",
    "profile",
    "pstats",
    "pty",
    "pwd",
    "py_compile",
    "pyclbr",
    "pydoc",
    "quopri",
    "random",
    "re",
    "readline",
    "resource",
    "rexec",
    "rfc822",
    "rlcompleter",
    "robotparser",
    "runpy",
    "sched",
    "select",
    "sets",
    "sgmllib",
    "sha",
    "shelve",
    "shlex",
    "shutil",
    "signal",
    "site",
    "smtpd",
    "smtplib",
    "sndhdr",
    "socket",
    "spwd",
    "sqlite3",
    "sre",
    "sre_compile",
    "sre_constants",
    "sre_parse",
    "ssl",
    "stat",
    "statvfs",
    "string",
    "stringprep",
    "struct",
    "subprocess",
    "sunau",
    "sunaudiodev",
    "symbol",
    "symtable",
    "sys",
    "sysconfig",
    "syslog",
    "tabnanny",
    "tarfile",
    "telnetlib",
    "tempfile",
    "termios",
    "test",
    "textwrap",
    "thread",
    "threading",
    "time",
    "timeit",
    "token",
    "tokenize",
    "trace",
    "traceback",
    "ttk",
    "tty",
    "turtle",
    "types",
    "unicodedata",
    "unittest",
    "urllib",
    "urllib2",
    "urlparse",
    "user",
    "uu",
    "uuid",
    "videoreader",
    "warnings",
    "wave",
    "weakref",
    "webbrowser",
    "whichdb",
    "winsound",
    "wsgiref",
    "xdrlib",
    "xml",
    "xmlrpclib",
    "zipfile",
    "zipimport",
    "zlib",
}
