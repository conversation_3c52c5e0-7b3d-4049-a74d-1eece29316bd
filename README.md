# Universal Wallet Platform

A production-ready fintech application designed to showcase the integration and management of dual wallet systems—Fiat and Cryptocurrency (Bitcoin)—within a single user-friendly ecosystem. Built with modern technologies and comprehensive security features.

## 🚀 Features

### Core Functionality
- **🏦 Dual Wallet System**: Manage both fiat (USD) and cryptocurrency (Bitcoin) wallets
- **🔐 Secure Authentication**: OAuth2 with JWT tokens, refresh token support
- **💸 Transaction Management**: Deposits, withdrawals, and conversions between wallets
- **🎫 Support Ticket System**: Create and manage support tickets with account manager assignment
- **🔄 Password Reset**: Secure password reset with time-limited tokens and email notifications
- **🆕 Public Registration**: Rate-limited user registration (3 requests per 30 minutes per IP)
- **🆕 Dynamic Withdraw Limits**: Real-time tracking of withdraw usage with admin-controlled resets

### KYC & Compliance
- **📋 KYC Profile System**: Complete Know Your Customer workflow
- **📸 Avatar Upload**: Profile image support (max 5MB, JPG/PNG)
- **✅ KYC Approval Workflow**: PENDING → UNDER_REVIEW → APPROVED/REJECTED
- **🛡️ Admin Controls**: Configurable withdraw limits for risk management

### Admin Features
- **👥 User Management**: View and manage all users and their limits
- **🏛️ Wallet Configuration**: Manage default crypto addresses for new users
- **📊 Transaction Oversight**: Monitor and manage all platform transactions
- **⚙️ System Administration**: Full control over platform settings and configurations

### Technical Excellence
- **🆔 UUID System**: All entities use UUIDs for enhanced security and scalability
- **🧪 Comprehensive Testing**: 66+ tests with 75% code coverage
- **📚 API Documentation**: Interactive Swagger UI and ReDoc documentation
- **🔒 Security First**: Input validation, rate limiting, and secure password handling

## 🛠️ Tech Stack

### Backend
- **FastAPI**: Modern, fast web framework for building APIs with automatic OpenAPI documentation
- **SQLModel**: Type-safe ORM built on SQLAlchemy 2.0 for database interactions
- **Alembic**: Database migration tool for schema versioning
- **SQLite/PostgreSQL**: Flexible database support (SQLite for development, PostgreSQL for production)
- **Pydantic**: Data validation and settings management using Python type annotations

### Development & Deployment
- **Poetry**: Modern dependency management and packaging
- **Docker**: Containerization for consistent deployment
- **Pytest**: Comprehensive testing framework with async support
- **Pre-commit**: Code quality and formatting hooks

### Security & Authentication
- **PassLib**: Secure password hashing with bcrypt
- **Python-JOSE**: JWT token handling for authentication
- **OAuth2**: Industry-standard authentication flows

## 🚀 Getting Started

### Prerequisites

- **Python 3.10+** (recommended)
- **Poetry** for dependency management
- **Docker & Docker Compose** (optional, for containerized deployment)

### Quick Start (Local Development)

1. **Clone the repository:**
```bash
git clone https://github.com/yourusername/universal-wallet-platform.git
cd universal-wallet-platform
```

2. **Install dependencies:**
```bash
poetry install
```

3. **Set up environment variables:**
```bash
cp .env.example .env
# Edit .env file with your settings
```

4. **Initialize the database:**
```bash
poetry run alembic upgrade head
```

5. **Create a superuser:**
```bash
DATABASE_URL="sqlite+aiosqlite:///./wallet_db.sqlite" poetry run python -m app.utils.create_superuser
```

6. **Run the application:**
```bash
DATABASE_URL="sqlite+aiosqlite:///./wallet_db.sqlite" poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

7. **Access the application:**
   - **API Documentation**: http://localhost:8000/api/v1/docs
   - **Alternative Docs**: http://localhost:8000/api/v1/redoc
   - **Health Check**: http://localhost:8000/health

### 🔑 Default Admin Credentials

After running the superuser creation script:
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: Admin (full access to all features)

### Docker Deployment

1. **Using Docker Compose:**
```bash
docker-compose up -d
```

2. **The API will be available at**: http://localhost:8000/api/v1/docs

## 📚 API Documentation

The platform provides comprehensive API documentation with interactive testing capabilities:

- **Swagger UI**: http://localhost:8000/api/v1/docs (Interactive API explorer)
- **ReDoc**: http://localhost:8000/api/v1/redoc (Clean documentation)
- **OpenAPI Schema**: http://localhost:8000/api/v1/openapi.json

### Key API Endpoints

#### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - Public user registration (rate-limited)
- `POST /api/v1/auth/refresh` - Refresh access token

#### User Management
- `GET /api/v1/users/me` - Get current user profile
- `GET /api/v1/users/me/withdraw-limits` - Get available withdraw limits
- `PUT /api/v1/users/{user_id}/withdraw-limits` - Update withdraw limits (Admin)
- `PUT /api/v1/users/{user_id}/reset-withdraw-usage` - Reset withdraw usage (Admin)
- `PUT /api/v1/users/{user_id}/reset-password` - Reset user password (Admin)

#### KYC & Profiles
- `GET /api/v1/profiles/me` - Get user profile
- `PUT /api/v1/profiles/me` - Update user profile
- `POST /api/v1/profiles/me/avatar` - Upload avatar image
- `POST /api/v1/profiles/me/submit-kyc` - Submit KYC for review
- `PUT /api/v1/profiles/{user_id}/kyc-review` - Review KYC (Admin)

#### Wallets
- `GET /api/v1/wallets/fiat/me` - Get fiat wallet
- `GET /api/v1/wallets/crypto/me` - Get crypto wallet

#### Transactions
- `GET /api/v1/transactions/me` - Get user transactions
- `POST /api/v1/transactions/withdraw` - Create withdrawal
- `POST /api/v1/transactions/convert` - Convert between wallets
- `POST /api/v1/transactions/deposit` - Create deposit (Admin)

#### Admin
- `GET /api/v1/admin/wallet-configs` - Manage wallet configurations
- `POST /api/v1/admin/wallet-configs` - Create wallet configuration
- `PUT /api/v1/admin/users/{user_id}/crypto-wallet-address` - Update crypto address

## 🧪 Testing

The project includes comprehensive test coverage with **66+ tests** achieving **75% code coverage**:

### Test Categories
- **Authentication & Security**: Login, registration, token handling
- **KYC & Profiles**: Profile management, avatar upload, KYC workflow
- **Wallet Operations**: Fiat and crypto wallet management
- **Transaction Logic**: Withdrawals, deposits, conversions, limits
- **Admin Features**: User management, wallet configuration
- **Password Reset**: Secure password reset workflow
- **API Endpoints**: All endpoints with error handling

### Running Tests

**Run all tests:**
```bash
poetry run pytest
```

**Run with coverage report:**
```bash
poetry run pytest --cov=app --cov-report=term-missing
```

**Run specific test categories:**
```bash
# Model tests
poetry run pytest tests/test_models.py

# API tests
poetry run pytest tests/api/

# Specific feature tests
poetry run pytest tests/api/test_profiles.py -v
```

## 📁 Project Structure

```
universal-wallet-platform/
├── alembic/                  # Database migrations
│   ├── versions/             # Migration files
│   └── env.py               # Alembic configuration
├── app/                      # Application code
│   ├── api/                  # API routes and dependencies
│   │   ├── deps.py          # Dependency injection
│   │   └── v1/              # API version 1
│   │       ├── api.py       # API router
│   │       └── endpoints/   # API endpoints
│   │           ├── admin.py         # Admin management
│   │           ├── auth.py          # Authentication
│   │           ├── password_reset.py # Password reset
│   │           ├── profiles.py      # KYC profiles
│   │           ├── support.py       # Support tickets
│   │           ├── transactions.py  # Transaction management
│   │           ├── users.py         # User management
│   │           └── wallets.py       # Wallet operations
│   ├── core/                 # Core functionality
│   │   ├── config.py        # Application settings
│   │   ├── logging.py       # Logging configuration
│   │   └── security.py      # Security utilities
│   ├── crud/                 # Database operations (CRUD)
│   │   ├── base.py          # Base CRUD operations
│   │   ├── user.py          # User operations
│   │   ├── wallet.py        # Wallet operations
│   │   └── transaction.py   # Transaction operations
│   ├── db/                   # Database connection
│   │   └── session.py       # Database session management
│   ├── models/               # SQLModel database models
│   │   ├── user.py          # User model
│   │   ├── user_profile.py  # KYC profile model
│   │   ├── wallet.py        # Wallet models
│   │   ├── wallet_config.py # Wallet configuration
│   │   ├── transaction.py   # Transaction model
│   │   ├── support.py       # Support ticket models
│   │   └── password_reset.py # Password reset model
│   ├── schemas/              # Pydantic schemas (API models)
│   │   ├── user.py          # User schemas
│   │   ├── user_profile.py  # Profile schemas
│   │   ├── wallet.py        # Wallet schemas
│   │   ├── transaction.py   # Transaction schemas
│   │   └── token.py         # Authentication schemas
│   ├── utils/                # Utility functions
│   │   ├── create_superuser.py # Superuser creation script
│   │   └── email.py         # Email utilities
│   └── main.py              # FastAPI application entry point
├── tests/                    # Comprehensive test suite
│   ├── api/                 # API endpoint tests
│   │   ├── test_admin.py    # Admin functionality tests
│   │   ├── test_auth.py     # Authentication tests
│   │   ├── test_profiles.py # KYC profile tests
│   │   └── test_transactions.py # Transaction tests
│   ├── test_models.py       # Database model tests
│   └── conftest.py          # Test configuration
├── docs/                     # Documentation
│   └── frontend-guide.md    # Frontend developer guide
├── .env.example              # Example environment variables
├── .env                      # Environment variables (local)
├── docker-compose.yml        # Docker Compose configuration
├── Dockerfile                # Docker configuration
├── pyproject.toml            # Poetry configuration and dependencies
├── alembic.ini              # Alembic configuration
└── README.md                # Project documentation
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Check the [Frontend Developer Guide](docs/frontend-guide.md) for integration help
- Review the API documentation at `/api/v1/docs`

## 🔗 Related Documentation

- [Frontend Developer Guide](docs/frontend-guide.md) - Complete guide for frontend integration
- [API Documentation](http://localhost:8000/api/v1/docs) - Interactive API explorer
- [Database Schema](alembic/versions/) - Database migration files
