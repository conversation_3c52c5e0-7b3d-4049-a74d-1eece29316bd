#!/usr/bin/env python3
"""
Comprehensive Live API Testing Script for Universal Wallet Platform

This script tests all major functionality of the API with real HTTP requests
to a running server. It simulates real-world scenarios and validates responses.

Usage:
    python scripts/live_api_test.py --base-url http://localhost:8000

Requirements:
    - Server must be running
    - Database must be initialized
    - Admin user must exist (or will be created)
"""

import argparse
import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional
import aiohttp
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('live_api_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class APITester:
    """Comprehensive API testing class."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.api_base = f"{self.base_url}/api/v1"
        self.session: Optional[aiohttp.ClientSession] = None
        self.admin_token: Optional[str] = None
        self.user_tokens: Dict[str, str] = {}
        self.test_results = {
            "total_tests": 0,
            "passed": 0,
            "failed": 0,
            "errors": [],
            "start_time": None,
            "end_time": None
        }
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        self.test_results["start_time"] = datetime.now()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
        self.test_results["end_time"] = datetime.now()
    
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test result."""
        self.test_results["total_tests"] += 1
        if success:
            self.test_results["passed"] += 1
            logger.info(f"✅ {test_name}: PASSED {details}")
        else:
            self.test_results["failed"] += 1
            self.test_results["errors"].append(f"{test_name}: {details}")
            logger.error(f"❌ {test_name}: FAILED {details}")
    
    async def make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict] = None,
        headers: Optional[Dict] = None,
        files: Optional[Dict] = None,
        expected_status: int = 200
    ) -> Dict[str, Any]:
        """Make HTTP request and validate response."""
        url = f"{self.api_base}{endpoint}"
        
        # Default headers
        default_headers = {"Content-Type": "application/json"}
        if headers:
            default_headers.update(headers)
        
        try:
            if files:
                # For file uploads, don't set Content-Type (let aiohttp handle it)
                default_headers.pop("Content-Type", None)
                form_data = aiohttp.FormData()
                for key, value in (data or {}).items():
                    form_data.add_field(key, value)
                for key, file_info in files.items():
                    form_data.add_field(key, file_info["data"], filename=file_info["filename"])
                
                async with self.session.request(method, url, data=form_data, headers=default_headers) as response:
                    response_data = await response.json() if response.content_type == 'application/json' else await response.text()
            else:
                async with self.session.request(method, url, json=data, headers=default_headers) as response:
                    response_data = await response.json() if response.content_type == 'application/json' else await response.text()
            
            if response.status != expected_status:
                logger.warning(f"Unexpected status {response.status} for {method} {endpoint}. Expected {expected_status}")
                logger.warning(f"Response: {response_data}")
            
            return {
                "status": response.status,
                "data": response_data,
                "success": response.status == expected_status
            }
        
        except Exception as e:
            logger.error(f"Request failed: {method} {endpoint} - {str(e)}")
            return {
                "status": 0,
                "data": {"error": str(e)},
                "success": False
            }
    
    async def test_health_check(self):
        """Test basic server health."""
        logger.info("🔍 Testing server health...")
        
        response = await self.make_request("GET", "/", expected_status=200)
        self.log_test(
            "Server Health Check",
            response["success"],
            f"Status: {response['status']}"
        )
    
    async def test_admin_login(self):
        """Test admin login or create admin user."""
        logger.info("🔐 Testing admin authentication...")
        
        # Try to login with default admin credentials
        login_data = {
            "username": "<EMAIL>",
            "password": "password"
        }
        
        # Use form data for OAuth2 login
        form_data = aiohttp.FormData()
        form_data.add_field("username", login_data["username"])
        form_data.add_field("password", login_data["password"])
        
        url = f"{self.api_base}/auth/login"
        try:
            async with self.session.post(url, data=form_data) as response:
                if response.status == 200:
                    data = await response.json()
                    self.admin_token = data["access_token"]
                    self.log_test("Admin Login", True, "Successfully logged in")
                    return
                else:
                    logger.info("Admin user doesn't exist, will create one...")
        except Exception as e:
            logger.info(f"Admin login failed: {e}, will create admin user...")
        
        # Create admin user if login failed
        await self.create_admin_user()
    
    async def create_admin_user(self):
        """Create admin user using the create superuser utility."""
        logger.info("👑 Creating admin user...")
        
        # This would typically be done via CLI, but for testing we'll assume it exists
        # In a real scenario, you'd run: python -m app.create_superuser
        
        # For now, let's try to create via API (this might fail if no admin exists)
        admin_data = {
            "email": "<EMAIL>",
            "password": "password",
            "first_name": "Admin",
            "last_name": "User",
            "role": "admin"
        }
        
        response = await self.make_request("POST", "/users/", admin_data, expected_status=200)
        if response["success"]:
            self.log_test("Admin User Creation", True, "Admin user created")
            await self.test_admin_login()  # Try login again
        else:
            self.log_test("Admin User Creation", False, f"Failed: {response['data']}")
    
    async def test_public_registration(self):
        """Test public user registration with rate limiting."""
        logger.info("📝 Testing public registration...")
        
        # Test successful registration
        user_data = {
            "email": f"testuser_{int(time.time())}@example.com",
            "password": "password123",
            "first_name": "Test",
            "last_name": "User"
        }
        
        response = await self.make_request("POST", "/auth/register", user_data)
        self.log_test(
            "Public Registration",
            response["success"] and response["data"].get("is_active") is False,
            f"User created as inactive: {response['data'].get('email', 'N/A')}"
        )
        
        # Test rate limiting (try to register 4 times quickly)
        logger.info("🛡️ Testing rate limiting...")
        rate_limit_hit = False
        for i in range(4):
            user_data["email"] = f"ratelimit_{i}_{int(time.time())}@example.com"
            response = await self.make_request("POST", "/auth/register", user_data, expected_status=429)
            if response["status"] == 429:
                rate_limit_hit = True
                break
        
        self.log_test(
            "Rate Limiting",
            rate_limit_hit,
            "Rate limit enforced after multiple requests"
        )
    
    async def test_user_management(self):
        """Test admin user management features."""
        if not self.admin_token:
            logger.warning("Skipping user management tests - no admin token")
            return
        
        logger.info("👥 Testing user management...")
        
        headers = {"Authorization": f"Bearer {self.admin_token}"}
        
        # Test user listing with pagination
        response = await self.make_request("GET", "/users/?page=1&page_size=10", headers=headers)
        self.log_test(
            "User Listing with Pagination",
            response["success"] and "results" in response["data"],
            f"Retrieved {len(response['data'].get('results', []))} users"
        )
        
        # Test creating a user
        new_user_data = {
            "email": f"adminuser_{int(time.time())}@example.com",
            "password": "password123",
            "first_name": "Admin",
            "last_name": "Created"
        }
        
        response = await self.make_request("POST", "/users/", new_user_data, headers=headers)
        if response["success"]:
            user_id = response["data"]["id"]
            self.log_test("Admin User Creation", True, f"User created with ID: {user_id}")
            
            # Test password reset
            reset_data = {"new_password": "newpassword123"}
            response = await self.make_request(
                "PUT", f"/users/{user_id}/reset-password", reset_data, headers=headers
            )
            self.log_test("Admin Password Reset", response["success"], "Password reset successfully")
            
        else:
            self.log_test("Admin User Creation", False, f"Failed: {response['data']}")
    
    async def generate_report(self):
        """Generate comprehensive test report."""
        duration = (self.test_results["end_time"] - self.test_results["start_time"]).total_seconds()
        
        report = f"""
🧪 UNIVERSAL WALLET PLATFORM - API TEST REPORT
{'='*60}

📊 Test Summary:
  Total Tests: {self.test_results['total_tests']}
  Passed: {self.test_results['passed']} ✅
  Failed: {self.test_results['failed']} ❌
  Success Rate: {(self.test_results['passed']/self.test_results['total_tests']*100):.1f}%
  Duration: {duration:.2f} seconds

🕐 Test Period:
  Start: {self.test_results['start_time']}
  End: {self.test_results['end_time']}

"""
        
        if self.test_results["errors"]:
            report += "❌ Failed Tests:\n"
            for error in self.test_results["errors"]:
                report += f"  - {error}\n"
        
        logger.info(report)
        
        # Save report to file
        with open("api_test_report.txt", "w") as f:
            f.write(report)
        
        return self.test_results["failed"] == 0


async def main():
    """Main test execution."""
    parser = argparse.ArgumentParser(description="Live API Testing for Universal Wallet Platform")
    parser.add_argument("--base-url", default="http://localhost:8000", help="Base URL of the API server")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info(f"🚀 Starting comprehensive API tests for {args.base_url}")
    
    async with APITester(args.base_url) as tester:
        # Run all tests
        await tester.test_health_check()
        await tester.test_admin_login()
        await tester.test_public_registration()
        await tester.test_user_management()
        
        # Generate final report
        success = await tester.generate_report()
        
        if success:
            logger.info("🎉 All tests passed!")
            sys.exit(0)
        else:
            logger.error("💥 Some tests failed!")
            sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
